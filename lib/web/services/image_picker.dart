import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';

class SelectedFile {
  final String name;
  final String extension;
  final Uint8List uInt8List;
  final String type;

  SelectedFile({
    required this.name,
    required this.extension,
    required this.uInt8List,
    required this.type, // 'image' or 'pdf'
  });
}

class ImagePickerService {
  Future<SelectedFile?> pickImageNew(BuildContext context,
      {required bool useCompressor}) async {
    try {
      final XFile? image =
          await ImagePicker().pickImage(source: ImageSource.gallery);
      if (image != null) {
        final nameSplits = image.name.split(".");

        final finalBytes = useCompressor
            ? await imageCompressor(await image.readAsBytes())
            : await image.readAsBytes();
        return SelectedFile(
            name: nameSplits.first,
            extension: nameSplits.length > 1 ? nameSplits.last : "",
            type: 'image',
            uInt8List: finalBytes);
      }

      return null;
    } catch (e) {
      debugPrint(e.toString());
      Clipboard.setData(ClipboardData(text: e.toString()));
      return null;
    }
  }

  Future<List<SelectedFile?>> pickFile(
      BuildContext context, bool allowmultiple) async {
    try {
      List<SelectedFile?> imageList = [];
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: allowmultiple,
        type: FileType.custom,
        allowedExtensions: ['jpg', 'jpeg', 'png', 'gif', 'pdf'],
        // Allowed image and PDF extensions
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final files = result.files;
        for (var file in files) {
          final nameSplits = file.name.split(".");
          final extension = nameSplits.length > 1 ? nameSplits.last : "";
          final fileBytes = file.bytes;

          if (fileBytes != null) {
            // Check file extension to determine the type
            if (extension.toLowerCase() == 'pdf') {
              // If it's a PDF file
              imageList.add(SelectedFile(
                name: nameSplits.first,
                extension: extension,
                uInt8List: fileBytes,
                type: 'pdf',
              ));
            } else if (extension.toLowerCase() == 'jpg' ||
                extension.toLowerCase() == 'jpeg' ||
                extension.toLowerCase() == 'png' ||
                extension.toLowerCase() == 'gif') {
              // If it's an image file
              imageList.add(SelectedFile(
                name: nameSplits.first,
                extension: extension,
                uInt8List: fileBytes,
                type: 'image',
              ));
            } else {
              // If it's neither an image nor a PDF, you can return null or handle the error
              debugPrint('Selected file is neither an image nor a PDF');
            }
          }
        }
        return imageList;
      }

      return [];
    } catch (e) {
      debugPrint("Error picking file: $e");
      return [];
    }
  }
}

Future<Uint8List> imageCompressor(Uint8List list) async {
  var result = await FlutterImageCompress.compressWithList(
    list,
    minHeight: 1920,
    minWidth: 1080,
    quality: 70,
  );
  return result;
}
