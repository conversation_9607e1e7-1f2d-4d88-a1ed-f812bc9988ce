import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:mass_ibs/modules/home/<USER>';
import 'package:mass_ibs/web/controller/home_controller.dart';
import 'package:mass_ibs/web/models/task_model.dart';
import 'package:mass_ibs/web/models/user_model.dart';
import 'package:mass_ibs/web/models/vehicle_model.dart';

class UserDocsModel {
  final String documentId;
  final String docName;
  final List<DocFiles> files;
  // final String fileUrl;
  // final String fileType;
  final String userName;
  final String userContact;
  final String uId;
  final String? vehicleId;
  final String? vehicleNum;
  final bool isDeleted;
  final bool isArchived;
  final bool vehicleDoc;
  final List<TaskDetailModel>? fieldData;

  UserDocsModel({
    required this.documentId,
    required this.docName,
    // required this.fileType,
    // required this.fileUrl,
    required this.files,
    required this.userContact,
    required this.userName,
    required this.isDeleted,
    required this.uId,
    required this.vehicleDoc,
    required this.isArchived,
    this.vehicleId,
    this.vehicleNum,
    required this.fieldData,
  });

  // factory UserDocsModel.fromJson(Map<String, dynamic> json) {
  //   return UserDocsModel(
  //       documentId: json['documentId'] ?? '',
  //       docName: json['docName'] ?? '',
  //       // fileUrl: json['fileUrl'] ?? '',
  //       // fileType: json['fileType'] ?? '',
  //       userContact: json['userContact'] ?? '',
  //       userName: json['userName'] ?? '',
  //       isDeleted: json['isDeleted'] ?? false,
  //       vehicleDoc: json['vehicleDoc'] ?? false,
  //       isArchived: json['isArchived'] ?? false,
  //       files: ((json['files'] ?? []) as List)
  //           .map((e) => DocFiles(
  //               fileType: (e as Map).keys.first, fileUrl: e.values.first))
  //           .toList(),
  //       fieldData: json['fieldData'] != null
  //           ? (json['fieldData'] as Map<String, dynamic>)
  //               .entries
  //               .map((e) => TaskDetailModel.fromJson(
  //                   e.key, e.value as Map<String, dynamic>))
  //               .toList()
  //           : null, // Handle fieldData as a List<TaskDetailModel>
  //       uId: json['uId'] ?? '',
  //       vehicleId: (json.containsKey('vehicleId') ? json['vehicleId'] : null),
  //       vehicleNum:
  //           (json.containsKey('vehicleNum') ? json['vehicleNum'] : null));
  // }
  factory UserDocsModel.fromSnapshot(QueryDocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    UserModel? userData;
    VehicleModel? vehicleData;
    try {
      userData = !data.containsKey('userName')
          ? Get.find<HomeCtrl>().users.firstWhereOrNull(
              (element) => element.docId == (data['uId'] ?? ''))
          : null;
      if (data['vehicleDoc'] || false) {
        vehicleData = !data.containsKey('vehicleNum')
            ? Get.find<HomeCtrl>().vehicles.firstWhereOrNull(
                (element) => element.docId == (data['vehicleId'] ?? ''))
            : null;
      }
    } catch (e) {
      userData = !data.containsKey('userName')
          ? Get.find<HomeController>().user
          : null;
      if (data['vehicleDoc'] || false) {
        vehicleData = !data.containsKey('vehicleNum')
            ? Get.find<HomeController>().vehicles.firstWhereOrNull(
                (element) => element.docId == (data['vehicleId'] ?? ''))
            : null;
      }
    }
    List<DocFiles> files = [];
    if (data.containsKey('files')) {
      files = ((data['files'] ?? []) as List)
          .map((e) => DocFiles(
              fileType: (e as Map).keys.first, fileUrl: e.values.first))
          .toList();

      if (data['fileUrl'] != null && data['fileUrl'] != '') {
        files.add(
            DocFiles(fileType: data['fileType'], fileUrl: data['fileUrl']));
      }
    } else if ((data['fileUrl'] != null && data['fileType'] != null) &&
        (data['fileUrl'] != '' && data['fileType'] != '')) {
      files = [DocFiles(fileType: data['fileType'], fileUrl: data['fileUrl'])];
    } else {
      files = [];
    }
    return UserDocsModel(
      documentId: snapshot.id,
      docName: data['docName'] ?? '',
      // fileUrl: data['fileUrl'] ?? '',
      // fileType: data['fileType'] ?? '',
      files: files,
      isDeleted: data['isDeleted'] ?? false,
      vehicleDoc: data['vehicleDoc'] ?? false,
      isArchived: data['isArchived'] ?? false,
      userContact:
          userData == null ? data['userContact'] ?? '' : userData.number,
      userName: userData == null ? data['userName'] ?? '' : userData.name,
      fieldData: data['fieldData'] != null
          ? (data['fieldData'] as Map<String, dynamic>)
              .entries
              .map((e) => TaskDetailModel.fromJson(
                  e.key, e.value as Map<String, dynamic>))
              .toList()
          : null,
      uId: data['uId'] ?? '',
      vehicleId: data['vehicleId'] ?? '',
      vehicleNum: vehicleData != null
          ? vehicleData.vehicleNumber
          : data['vehicleNum'] ?? '',
    );
  }
  Map<String, dynamic> toJson() => {
        'documentId': documentId,
        'docName': docName,
        // 'fileUrl': fileUrl,
        'vehicleDoc': vehicleDoc,
        // 'fileType': fileType,
        'userContact': userContact,
        'files': files.map((e) => e.toMap()).toList(),
        'userName': userName,
        'isDeleted': isDeleted,
        'isArchived': isArchived,
        'uId': uId,
        'vehicleId': vehicleId,
        'vehicleNum': vehicleNum,
        'fieldData': fieldData?.map((e) => e.toJson()).toList(),
      };
}

class DocFiles {
  final String fileUrl;
  final String fileType;
  DocFiles({
    required this.fileType,
    required this.fileUrl,
  });
  Map<String, dynamic> toMap() {
    return {fileType: fileUrl};
  }
}
