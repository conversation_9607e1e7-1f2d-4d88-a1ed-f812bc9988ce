import 'package:cloud_firestore/cloud_firestore.dart';

class SettingModel {
  final String password;
  final bool userAddDocPermission;
  SettingModel({
    required this.password,
    required this.userAddDocPermission,
  });
  factory SettingModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return SettingModel(
      password: data['password'] ?? '',
      userAddDocPermission: data['userAddDocPermission'] ?? true,
    );
  }
}
