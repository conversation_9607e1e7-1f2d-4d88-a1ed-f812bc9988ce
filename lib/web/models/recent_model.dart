import 'package:cloud_firestore/cloud_firestore.dart';

class RecentModel {
  final String docId;
  final String userId;
  final String name;
  final String email;
  final String number;
  final String message;
  final Timestamp? createdAt;

  RecentModel({
    required this.docId,
    required this.userId,
    required this.name,
    required this.email,
    required this.number,
    required this.message,
    required this.createdAt,
  });

  factory RecentModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return RecentModel(
      docId: json.id,
      userId: json['userid'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      number: json['number'] as String,
      message: json['message'] as String,
      createdAt: json['createdAt'],
    );
  }

  factory RecentModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return RecentModel(
      docId: json.id,
      userId: json['userid'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      number: json['number'] as String,
      message: json['message'] as String,
      createdAt: json['createdAt'],
    );
  }

  factory RecentModel.fromJson(Map<String, dynamic> json) {
    return RecentModel(
      docId: json['docId'] as String,
      userId: json['userid'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      number: json['number'] as String,
      message: json['message'] as String,
      createdAt: json['createdAt'],
    );
  }

  Map<String, dynamic> toJson() => {
        'docId': docId,
        'userid': userId,
        'name': name,
        'email': email,
        'number': number,
        'message': message,
        'createdAt': createdAt?.toDate().toIso8601String(),
      };
}
