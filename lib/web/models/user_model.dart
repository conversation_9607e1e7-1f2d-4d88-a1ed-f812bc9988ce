import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String docId;
  final String userId;
  final String name;
  final String number;
  final String email;
  final String firebaseToken;
  final Timestamp? createdAt;
  // final List<UserDocumentModel> documents;

  UserModel({
    required this.docId,
    required this.userId,
    required this.name,
    required this.number,
    required this.email,
    required this.firebaseToken,
    required this.createdAt,
    // required this.documents,
  });

  factory UserModel.fromSnap(QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return UserModel(
      docId: json.id,
      userId: json['userid'] as String,
      name: json['name'] as String,
      number: json['number'] as String,
      email: json['email'] as String,
      firebaseToken: json['firebaseToken'] as String,
      createdAt: json['createdAt'],
      // documents: (json['documents'] as List<dynamic>?)
      //         ?.map(
      //             (e) => UserDocumentModel.fromJson(e as Map<String, dynamic>))
      //         .toList() ??
      //     [],
    );
  }

  factory UserModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return UserModel(
      docId: json.id,
      userId: json['userid'] as String,
      name: json['name'] as String,
      number: json['number'] as String,
      createdAt: json['createdAt'],
      email: json['email'] as String,
      firebaseToken: json['firebaseToken'] as String,
      // documents: (json['documents'] as List<dynamic>?)
      //         ?.map(
      //             (e) => UserDocumentModel.fromJson(e as Map<String, dynamic>))
      //         .toList() ??
      //     [],
    );
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      docId: json['docId'] as String,
      userId: json['userid'] as String,
      name: json['name'] as String,
      number: json['number'] as String,
      createdAt: json['createdAt'],
      email: json['email'] as String,
      firebaseToken: json['firebaseToken'] as String,
      // documents: (json['documents'] as List<dynamic>?)
      //         ?.map(
      //             (e) => UserDocumentModel.fromJson(e as Map<String, dynamic>))
      //         .toList() ??
      //     [],
    );
  }

  Map<String, dynamic> toJson() => {
        'docId': docId,
        'userid': userId,
        'name': name,
        'number': number,
        'email': email,
        'firebaseToken': firebaseToken,
        'createdAt': createdAt?.toDate().toIso8601String(),
        // 'documents': documents.map((e) => e.toJson()).toList(),
      };
}

// class UserDocumentModel {
//   final String documentId;
//   final String docName;
//   final String fileUrl;
//   final String fileType;
//   final bool isDeleted;
//   final List<TaskDetailModel>? fieldData;

//   UserDocumentModel({
//     required this.documentId,
//     required this.docName,
//     required this.fileType,
//     required this.fileUrl,
//     required this.isDeleted,
//     this.fieldData,
//   });

//   factory UserDocumentModel.fromJson(Map<String, dynamic> json) {
//     return UserDocumentModel(
//       documentId: json['documentId'] ?? '',
//       docName: json['docName'] ?? '',
//       fileUrl: json['fileUrl'] ?? '',
//       fileType: json['fileType'] ?? '',
//       isDeleted: json['isDeleted'] ?? false,
//       fieldData: json['fieldData'] != null
//           ? (json['fieldData'] as List<dynamic>)
//               .map((e) => TaskDetailModel.fromJson(e as Map<String, dynamic>))
//               .toList()
//           : null, // Handle fieldData as a List<TaskDetailModel>
//     );
//   }

//   Map<String, dynamic> toJson() => {
//         'documentId': documentId,
//         'docName': docName,
//         'fileUrl': fileUrl,
//         'fileType': fileType,
//         'isDeleted': isDeleted,
//         'fieldData': fieldData?.map((e) => e.toJson()).toList(),
//       };
// }
