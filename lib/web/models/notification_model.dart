import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationModel {
  final String docId;
  final String title;
  final String desc;
  final bool? test;

  NotificationModel({
    required this.docId,
    required this.title,
    required this.desc,
    required this.test,
  });

  factory NotificationModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return NotificationModel(
      docId: json.id,
      title: json['title'] as String,
      desc: json['desc'] as String,
      test: json['test'] as bool,
    );
  }

  factory NotificationModel.fromDocSnap(
      DocumentSnapshot<Map<String, dynamic>> json) {
    return NotificationModel(
      docId: json.id,
      title: json['title'] as String,
      desc: json['desc'] as String,
      test: json['test'] as bool,
    );
  }

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      docId: json['docId'] as String,
      title: json['title'] as String,
      desc: json['desc'] as String,
      test: json['test'] as bool,
    );
  }

  Map<String, dynamic> toJson() => {
        'docId': docId,
        'title': title,
        'desc': desc,
        'test': test,
      };
}
