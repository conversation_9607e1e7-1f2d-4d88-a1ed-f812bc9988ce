import 'package:cloud_firestore/cloud_firestore.dart';

class NewsModel {
  final String docId;
  final String title;
  final String description;

  NewsModel({
    required this.docId,
    required this.title,
    required this.description,
  });

  factory NewsModel.fromSnap(QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return NewsModel(
      docId: json.id,
      title: json['title'] as String,
      description: json['description'] as String,
    );
  }

  factory NewsModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return NewsModel(
      docId: json.id,
      title: json['title'] as String,
      description: json['description'] as String,
    );
  }

  factory NewsModel.fromJson(Map<String, dynamic> json) {
    return NewsModel(
      docId: json['docId'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
        'docId': docId,
        'title': title,
        'description': description,
      };
}
