import 'package:cloud_firestore/cloud_firestore.dart';

class BannerModel {
  final String docId;
  final String title;
  final String link;
  final String image;

  BannerModel({
    required this.docId,
    required this.title,
    required this.link,
    required this.image,
  });

  factory BannerModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> json) {
    return BannerModel(
      docId: json.id,
      title: json['title'] as String,
      link: json['link'] as String,
      image: json['image'] as String,
    );
  }

  factory BannerModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> json) {
    return BannerModel(
      docId: json.id,
      title: json['title'] as String,
      link: json['link'] as String,
      image: json['image'] as String,
    );
  }

  factory BannerModel.fromJson(Map<String, dynamic> json) {
    return BannerModel(
      docId: json['docId'] as String,
      title: json['title'] as String,
      link: json['link'] as String,
      image: json['image'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
        'docId': docId,
        'title': title,
        'link': link,
        'image': image,
      };
}
