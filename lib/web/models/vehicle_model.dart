import 'package:cloud_firestore/cloud_firestore.dart';

class VehicleModel {
  final String docId;
  final String userId;
  final String vehicleName;
  final String vehicleNumber;
  final String chassisNumber;
  final String vehicleType;
  final Timestamp? createdAt;
  final bool? isDeleted;
  // final List<VehicleDocumentModel> vehicleDocuments;

  VehicleModel({
    required this.docId,
    required this.userId,
    required this.vehicleName,
    required this.chassisNumber,
    required this.vehicleNumber,
    required this.vehicleType,
    required this.createdAt,
    required this.isDeleted,
    // required this.vehicleDocuments,
  });

  factory VehicleModel.fromSnap(
      QueryDocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data();

    return VehicleModel(
      docId: snap.id,
      userId: data['userId'] ?? '',
      vehicleName: data['vehicleName'] ?? '',
      chassisNumber: data['chassisNumber'] ?? '',
      vehicleNumber: data['vehicleNumber'] ?? '',
      vehicleType: data['vehicleType'] ?? '',
      createdAt: data['createdAt'],
      isDeleted: data['isDeleted'] ?? false,
      // vehicleDocuments: (data['vehicleDocuments'] as List<dynamic>?)
      //         ?.map((e) =>
      //             VehicleDocumentModel.fromJson(e as Map<String, dynamic>))
      //         .toList() ??
      //     [],
    );
  }

  Map<String, dynamic> toJson() => {
        'docId': docId,
        'userId': userId,
        'vehicleName': vehicleName,
        'vehicleNumber': vehicleNumber,
        'chassisNumber': chassisNumber,
        'vehicleType': vehicleType,
        'createdAt': createdAt,
        'isDeleted': isDeleted,
        // 'vehicleDocuments': vehicleDocuments.map((e) => e.toJson()).toList(),
      };
}

// class VehicleDocumentModel {
//   final String documentId;
//   final String docName;
//   final String fileType;
//   final String fileUrl;
//   final bool? isDeleted;
//   final List<TaskDetailModel>? fieldData;

//   VehicleDocumentModel({
//     required this.documentId,
//     required this.docName,
//     required this.fileUrl,
//     required this.fileType,
//     required this.isDeleted,
//     this.fieldData,
//   });

//   factory VehicleDocumentModel.fromJson(Map<String, dynamic> json) {
//     return VehicleDocumentModel(
//       documentId: json['documentId'] ?? '',
//       docName: json['docName'] ?? '',
//       fileUrl: json['fileUrl'] ?? '',
//       fileType: json['fileType'] ?? '',
//       isDeleted: json['isDeleted'] ?? false,
//       fieldData: json['fieldData'] != null
//           ? (json['fieldData'] as List<dynamic>)
//               .map((e) => TaskDetailModel.fromJson(e as Map<String, dynamic>))
//               .toList()
//           : null,
//     );
//   }

//   Map<String, dynamic> toJson() => {
//         'documentId': documentId,
//         'docName': docName,
//         'fileUrl': fileUrl,
//         'fileType': fileType,
//         'isDeleted': isDeleted,
//         'fieldData': fieldData?.map((e) => e.toJson()).toList(),
//       };
// }
