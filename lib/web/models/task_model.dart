import 'package:cloud_firestore/cloud_firestore.dart';

class TaskModel {
  final String docId;
  final String taskName;
  final Timestamp? createdAt;
  final bool? isVehicle;
  List<TaskDetailModel> taskDetails;

  TaskModel({
    required this.docId,
    required this.taskName,
    required this.createdAt,
    required this.isVehicle,
    this.taskDetails = const [], // Default to empty list if not provided
  });

  // From Firestore QueryDocumentSnapshot
  factory TaskModel.fromSnap(QueryDocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data();
    return TaskModel(
      docId: snap.id,
      taskName: data['taskName'] ?? '',
      createdAt: data['createdAt'],
      isVehicle: data['isVehicle'] ?? false,
      taskDetails: (data['fields'] as List<dynamic>?)
              ?.map((e) => TaskDetailModel.fromJson(
                  e['title'], e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  // From Firestore DocumentSnapshot
  factory TaskModel.fromDocSnap(DocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data()!;
    return TaskModel(
      docId: snap.id,
      taskName: data['taskName'] ?? '',
      createdAt: data['createdAt'],
      isVehicle: data['isVehicle'] ?? false,
      taskDetails: (data['fields'] as List<dynamic>?)
              ?.map((e) => TaskDetailModel.fromJson(
                  e['title'], e as Map<String, dynamic>))
              .toList() ??
          [],
    );
  }

  // From regular JSON (e.g., from REST API or local storage)
  factory TaskModel.fromJson(Map<String, dynamic> json) {
    return TaskModel(
        docId: json['docId'] ?? '',
        taskName: json['taskName'] ?? '',
        createdAt: json['createdAt'],
        isVehicle: json['isVehicle'] ?? false,
        taskDetails: (json['fields'] as List<dynamic>?)
                ?.map((e) => TaskDetailModel.fromJson(
                    e['title'], e as Map<String, dynamic>))
                .toList() ??
            []);
  }

  // Convert model to JSON (e.g., for storing in Firestore)
  Map<String, dynamic> toJson() => {
        'docId': docId,
        'taskName': taskName,
        'createdAt': createdAt?.toDate().toIso8601String(),
        'isVehicle': isVehicle,
        'fields': taskDetails.map((e) => e.toJson()).toList(),
      };
}

class TaskDetailModel {
  final String fieldId;
  final String type;
  final String title;
  dynamic value;
  bool required;

  TaskDetailModel({
    required this.fieldId,
    required this.type,
    required this.title,
    required this.value,
    required this.required,
  });

  // From Firestore QueryDocumentSnapshot
  factory TaskDetailModel.fromSnap(
      String title, QueryDocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data();
    return TaskDetailModel(
      fieldId: data['fieldId'] ?? '',
      type: data['type'] ?? '',
      title: data['title'] ?? '',
      value: data['value'] ?? '',
      required: data['required'] ?? false,
    );
  }

  // From Firestore DocumentSnapshot
  factory TaskDetailModel.fromDocSnap(
      String title, DocumentSnapshot<Map<String, dynamic>> snap) {
    final data = snap.data()!;
    return TaskDetailModel(
      fieldId: data['fieldId'] ?? '',
      type: data['type'] ?? '',
      title: data['title'] ?? '',
      value: data['value'] ?? '',
      required: data['required'] ?? false,
    );
  }

  // From JSON
  factory TaskDetailModel.fromJson(String title, Map<String, dynamic> json) {
    return TaskDetailModel(
      fieldId: json['fieldId'] ?? '',
      type: json['type'] ?? '',
      title: json['title'] ?? '',
      value: json['value'] ?? '',
      required: json['required'] ?? false,
    );
  }

  // To JSON
  Map<String, dynamic> toJson() => {
        title.replaceAll(' ', '_'): {
          'fieldId': fieldId,
          'title': title,
          'type': type,
          'value': value,
          'required': required,
        }
      };
}
