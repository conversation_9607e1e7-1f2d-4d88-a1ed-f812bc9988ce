import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/shared/firebase.dart';
import 'package:mass_ibs/web/models/banner_model.dart';
import 'package:mass_ibs/web/models/recent_model.dart';
import 'package:mass_ibs/web/models/setting_model.dart';
import 'package:mass_ibs/web/models/vehicle_model.dart';

import '../models/news_model.dart';
import '../models/task_model.dart';
import '../models/user_model.dart';

class HomeCtrl extends GetxController {
  List<UserModel> users = [];
  RxBool isUserLoading = false.obs;
  List<NewsModel> newsList = [];
  RxBool isNewsLoading = false.obs;
  List<TaskModel> tasks = [];
  RxBool isTaskLoading = false.obs;
  List<BannerModel> banners = [];
  RxBool isBannersLoading = false.obs;
  List<VehicleModel> vehicles = [];
  RxBool isVehiclesLoading = false.obs;
  List<RecentModel> recentList = [];
  RxBool isRecentLoading = false.obs;
  SettingModel? setting;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? settingstream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? usersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? tasksStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? bannersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? vehiclesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? newsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? recentStream;

  @override
  void onInit() {
    getUsers();
    getSetting();
    getTasks();
    getBanners();
    getVehicles();
    getNews();
    getRecents();
    super.onInit();
  }

  getUsers() async {
    isUserLoading(true);
    try {
      usersStream?.cancel();
      usersStream = FBFireStore.users.snapshots().listen((event) {
        users = event.docs
            .map((e) {
              try {
                return UserModel.fromSnap(e);
              } catch (err) {
                debugPrint("Error parsing document: ${err.toString()}");
                return null;
              }
            })
            .whereType<UserModel>()
            .toList();

        // Sort by createdAt, handling nulls (put nulls at the end)
        users.sort((a, b) {
          final aDate = a.createdAt?.toDate();
          final bDate = b.createdAt?.toDate();
          if (aDate == null && bDate == null) return 0;
          if (aDate == null) return 1;
          if (bDate == null) return -1;
          return bDate.compareTo(aDate); // latest first
        });

        debugPrint("Fetched ${users.length} users.");
        isUserLoading(false);
        update();
      });
    } catch (e) {
      isUserLoading(false);
      debugPrint("❌ Error in getUsers: ${e.toString()}");
    }
  }

  getTasks() async {
    isTaskLoading(true);
    try {
      tasksStream?.cancel();
      tasksStream = FBFireStore.tasks.snapshots().listen((event) {
        tasks = event.docs.map((e) => TaskModel.fromSnap(e)).toList();
        isTaskLoading(false);
        update();
      });
    } catch (e) {
      isTaskLoading(false);
      debugPrint('Error fetching tasks: ${e.toString()}');
    }
  }

  getSetting() async {
    try {
      settingstream?.cancel();
      settingstream = FBFireStore.setting.snapshots().listen((event) {
        setting = SettingModel.fromSnapshot(event);

        update();
      });
    } catch (e) {
      isTaskLoading(false);
      debugPrint('Error fetching tasks: ${e.toString()}');
    }
  }

  getBanners() async {
    isBannersLoading(true);
    try {
      bannersStream?.cancel();
      bannersStream = FBFireStore.banners.snapshots().listen((event) {
        banners = event.docs.map((e) => BannerModel.fromSnap(e)).toList();
        isBannersLoading(false);
        update();
      });
    } catch (e) {
      isBannersLoading(false);
      debugPrint(e.toString());
    }
  }

  getVehicles() async {
    isVehiclesLoading(true);
    try {
      // Cancel previous stream if it exists
      vehiclesStream?.cancel();

      // Listen for Firestore changes
      vehiclesStream = FBFireStore.vehicles.snapshots().listen((event) {
        // Print document data for debugging purposes to check its structure
        // for (var doc in event.docs) {
        //   debugPrint("Document Data ==> ${doc.data()}");
        // }

        // Convert Firestore data into VehicleModel list
        vehicles = event.docs.map((e) {
          // final data = e.data();
          return VehicleModel.fromSnap(e); // Create VehicleModel from document
        }).toList();
        isVehiclesLoading(false);

        // Update UI after fetching the data
        update();
      });
    } catch (e) {
      isVehiclesLoading(false);
      debugPrint("Error: ${e.toString()}");
    }
  }

  getNews() async {
    isNewsLoading(true);

    try {
      newsStream?.cancel();
      newsStream = FBFireStore.news.snapshots().listen((event) {
        newsList = event.docs.map((e) => NewsModel.fromSnap(e)).toList();
        isNewsLoading(false);
        update();
      });
    } catch (e) {
      isNewsLoading(false);

      debugPrint(e.toString());
    }
  }

  getRecents() async {
    isRecentLoading(true);

    try {
      recentStream?.cancel();
      recentStream = FBFireStore.recents.snapshots().listen((event) {
        recentList = event.docs.map((e) => RecentModel.fromSnap(e)).toList()
          ..sort((a, b) => b.createdAt!.compareTo(a.createdAt!));

        isRecentLoading(false);
        update();
      });
    } catch (e) {
      isRecentLoading(false);
      debugPrint(e.toString());
    }
  }

  getVehiclesByUser(String userId) async {
    try {
      // Cancel any previous stream
      vehiclesStream?.cancel();

      // Update the stream to only listen to vehicles belonging to the specific userId
      vehiclesStream = FBFireStore.vehicles
          .where('userId', isEqualTo: userId) // Filter by userId
          .snapshots()
          .listen((event) {
        vehicles = event.docs.map((e) => VehicleModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
