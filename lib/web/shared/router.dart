import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/web/views/add_banner/add_banner_page.dart';
import 'package:mass_ibs/web/views/news/news_home_page.dart';
import 'package:mass_ibs/web/views/news/news_web_details.dart';
import 'package:mass_ibs/web/views/notifications/notification_hisrtory_page.dart';
import 'package:mass_ibs/web/views/task_manager/task_manager_page.dart';
import 'package:mass_ibs/web/views/task_manager/widgets/task_details.dart';
import 'package:mass_ibs/web/views/vehicles/vehicle_details.dart';

import '../controller/home_controller.dart';
import '../models/vehicle_model.dart';
import '../views/authentication/signin.dart';
import '../views/news/add_news_page.dart';
import '../views/notifications/notifications_page.dart';
import '../views/policies/policies_page.dart';
import '../views/recents/recents_page.dart';
import '../views/users/users_page.dart';
import '../views/users/widgets/users_details.dart';
import '../views/vehicles/vehicles_page.dart';
import '../views/wrapper/wrapper.dart';
import 'error_page.dart';
import 'methods.dart';

const homeRoute = WebRoutes.users;

final GoRouter appRouter = GoRouter(
  debugLogDiagnostics: true,
  initialLocation: WebRoutes.signin,
  routes: _routes,
  redirect: redirector,
  errorBuilder: (context, state) => const ErrorPage(),
);

FutureOr<String?> redirector(BuildContext context, GoRouterState state) {
  // routeHistory.add(state.uri.path);
  // if (isLoggedIn() && state.fullPath == Routes.auth) {
  //   return routeHistory.reversed.elementAt(1);
  //   // return Routes.home;
  // }
  if (isLoggedIn()) {
    if (state.fullPath == WebRoutes.signin) {
      if (Get.isRegistered<HomeCtrl>()) {
        Future.delayed(const Duration(milliseconds: 10))
            .then((value) => Get.find<HomeCtrl>().update());
      }
      return homeRoute;
    } else {
      if (Get.isRegistered<HomeCtrl>()) Get.find<HomeCtrl>().update();
      return null;
    }
  } else {
    return WebRoutes.signin;
  }
}

List<RouteBase> get _routes {
  return <RouteBase>[
    ShellRoute(
      builder: (context, state, child) {
        if (!Get.isRegistered<HomeCtrl>()) {
          Get.put(HomeCtrl());
        }
        return DashboardScreen(child: child);
      },
      routes: [
        GoRoute(
          path: WebRoutes.vehicles,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(
            child: VehiclesPage(),
          ),
        ),
        GoRoute(
          path: '${WebRoutes.vehicle}/:id',
          pageBuilder: (BuildContext context, GoRouterState state) {
            final VehicleModel vehicleModel =
                state.extra as VehicleModel; // Retrieve the extra data
            return NoTransitionPage(
              child: VehicleDetails(
                vehicleModel: vehicleModel,
              ), // Pass the model to the details page
            );
          },
        ),
        GoRoute(
          path: WebRoutes.policies,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(
            child: PoliciesPage(),
          ),
        ),
        GoRoute(
          path: WebRoutes.users,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(
            child: UsersPage(),
          ),
        ),
        GoRoute(
          path: '${WebRoutes.user}/:id',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              NoTransitionPage(
            child: UserDetails(
              contact: state.uri.queryParameters['contact'] ?? '',
              userId: state.pathParameters['id']!,
              userName: state.uri.queryParameters['name'] ?? '',
            ),
          ),
        ),
        GoRoute(
          path: '${WebRoutes.addNews}/:id',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              NoTransitionPage(
            child: AddNewsPage(
              id: state.pathParameters['id']!,
              title: state.uri.queryParameters['title'] ?? '',
              description: state.uri.queryParameters['desc'] ?? '',
            ),
          ),
        ),
        GoRoute(
          path: WebRoutes.news,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(
            child: NewsPage(),
          ),
        ),
        GoRoute(
          path: '${WebRoutes.newDetails}/:id',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              NoTransitionPage(
            child: NewsDetailsPage(
              id: state.pathParameters['id']!,
              title: state.uri.queryParameters['title'] ?? '',
              description: state.uri.queryParameters['desc'] ?? '',
            ),
          ),
        ),
        GoRoute(
          path: WebRoutes.notifications,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(
            child: NotificationsPage(),
          ),
        ),
        GoRoute(
          path: WebRoutes.tasks,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(
            child: TaskManagerPage(),
          ),
        ),
        GoRoute(
          path: '${WebRoutes.task}/:id',
          pageBuilder: (BuildContext context, GoRouterState state) =>
              NoTransitionPage(
            child: TaskDetails(
              taskId: state.pathParameters['id']!,
              taskName: state.uri.queryParameters['name'] ?? '',
            ),
          ),
        ),
        GoRoute(
          path: WebRoutes.banner,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(
            child: AddBannerPage(),
          ),
        ),
        GoRoute(
          path: WebRoutes.notiHistory,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(
            child: NotiHistory(),
          ),
        ),
        GoRoute(
          path: WebRoutes.recentActivity,
          pageBuilder: (BuildContext context, GoRouterState state) =>
              const NoTransitionPage(
            child: RecentsPage(),
          ),
        ),
      ],
    ),
    GoRoute(
      path: WebRoutes.signin,
      pageBuilder: (BuildContext context, GoRouterState state) =>
          const NoTransitionPage(
        child: LoginPage(),
      ),
    ),
  ];
}

class WebRoutes {
  static const signin = '/signin';

  static const users = '/users';
  static const user = '/user';

  static const policies = '/policies';
  static const policy = '/policy';

  static const vehicles = '/vehicles';
  static const vehicle = '/vehicles';

  static const news = '/news';
  static const addNews = '/addNews';
  static const newDetails = '/newDetails';

  static const notifications = '/notifications';

  static const tasks = '/tasks';
  static const task = '/task';

  static const banner = '/banner';

  static const notiHistory = '/notiHistory';

  static const recentActivity = '/recentActivity';
}

// return isLoggedIn()
//     ? (state.uri.path == Routes.auth ? Routes.home : null)
//     : Routes.auth;
