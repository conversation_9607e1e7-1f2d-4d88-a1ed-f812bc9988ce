import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:date_format_field/date_format_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/web/models/user_docs_model.dart';
import 'package:mass_ibs/web/models/user_model.dart';
import 'package:mass_ibs/web/shared/router.dart';
import 'package:mass_ibs/web/shared/theme.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../shared/constants/colors.dart';
import '../../../shared/constants/svgs_constants.dart';
import '../../../shared/firebase.dart';
import '../../../shared/widgets/universal_image.dart';
import '../../controller/home_controller.dart';
import '../../models/task_model.dart';
import '../../models/vehicle_model.dart';
import '../../services/image_picker.dart';
import '../../shared/methods.dart';
import '../common/page_header.dart';

class VehicleDetails extends StatefulWidget {
  const VehicleDetails({
    super.key,
    required this.vehicleModel,
  });

  final VehicleModel vehicleModel;

  @override
  State<VehicleDetails> createState() => _VehicleDetailsState();
}

class _VehicleDetailsState extends State<VehicleDetails> {
  TextEditingController searchController = TextEditingController();

  String? selectedTaskName;
  List<String> taskNames = [];
  List<TaskModel> taskModels = [];
  String? name;
  @override
  void initState() {
    super.initState();
    _loadTaskModels();
    loadUserName();
  }

  loadUserName() async {
    final data = await FBFireStore.users.doc(widget.vehicleModel.userId).get();
    name = data.data()?['name'];
    if (mounted) {
      setState(() {});
    }
  }

  Future<void> _loadTaskModels() async {
    try {
      final taskCollection = await FBFireStore.tasks.get();
      List<TaskModel> fetchedTaskModels = [];

      for (var doc in taskCollection.docs) {
        final taskModel = TaskModel.fromSnap(doc); // Convert to TaskModel

        // Handle empty taskDetails safely
        List<TaskDetailModel> taskDetails = [];
        if (doc.data()['fields'] != null) {
          taskDetails = (doc.data()['fields'] as List<dynamic>)
              .map((e) => TaskDetailModel.fromJson(
                  e['title'], e as Map<String, dynamic>))
              .toList();
        }

        // Directly assigning the updated task details
        taskModel.taskDetails = taskDetails;
        fetchedTaskModels.add(taskModel); // Add to the list
      }

      setState(() {
        taskModels = fetchedTaskModels;
        selectedTaskName =
            taskModels.isNotEmpty ? taskModels.first.taskName : null;
      });
    } catch (e) {
      debugPrint("Error loading task models: $e");
    }
    debugPrint("Task models: $taskModels");
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(
            onRedirect: () {
              // context.go('${WebRoutes.users}/${widget.vehicleModel.userId}');
              context.push(
                '${WebRoutes.user}/${widget.vehicleModel.userId}?name=${Uri.encodeComponent(name ?? "")}?contact=${Uri.encodeComponent("")}',
              );
            },
            userName: '- (${name?.toUpperCase() ?? ''})',
            title:
                "${widget.vehicleModel.vehicleName.toUpperCase()} - ${widget.vehicleModel.vehicleNumber.toUpperCase()}",
            button: true,
            back: true,
            buttonName: 'New',
            icon: CupertinoIcons.add,
            onPressed: () {
              showTaskSelectionDialog(context);
            },
          ),
          Divider(),
          const SizedBox(height: 20),
          StreamBuilder(
              stream: FBFireStore.usersDocs
                  .where('vehicleDoc', isEqualTo: true)
                  .where('uId', isEqualTo: widget.vehicleModel.userId)
                  .where('vehicleId', isEqualTo: widget.vehicleModel.docId)
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return Text("Facing some issue!!");
                } else if (snapshot.hasData) {
                  return GetBuilder<HomeCtrl>(builder: (homeController) {
                    List<VehicleModel> vehicles = homeController.vehicles
                        .where(
                            (user) => user.userId == widget.vehicleModel.userId)
                        .toList();

                    // return Wrap(
                    //   spacing: AppInsets.s16,
                    //   runSpacing: AppInsets.s16,
                    //   children: List.generate(vehicles.length, (index) {
                    List<UserDocsModel> documents = snapshot.data?.docs
                            .map((e) => UserDocsModel.fromSnapshot(e))
                            .toList() ??
                        [];

                    // vehicles[index].vehicleDocuments;
                    return Wrap(
                      spacing: AppInsets.s16,
                      runSpacing: AppInsets.s16,
                      children: List.generate(documents.length, (index) {
                        return InkWell(
                          highlightColor: Colors.transparent,
                          overlayColor:
                              const WidgetStatePropertyAll(Colors.transparent),
                          hoverColor: Colors.transparent,
                          onTap: () {
                            // TODO:
                          },
                          child: Stack(
                            clipBehavior: Clip.antiAliasWithSaveLayer,
                            children: [
                              Container(
                                constraints: const BoxConstraints(
                                  maxWidth: 200,
                                  maxHeight: 144,
                                  minWidth: 200,
                                  minHeight: 144,
                                ),
                                padding: const EdgeInsets.symmetric(
                                    vertical: AppInsets.s12),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.circular(AppInsets.s4),
                                  border: Border.all(
                                    color: ColorConstants.borderColor,
                                    width: AppInsets.s1,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: AppInsets.pageMargin),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceEvenly,
                                    children: [
                                      Container(
                                        padding:
                                            const EdgeInsets.all(AppInsets.s6),
                                        width: AppInsets.s56,
                                        height: AppInsets.s56,
                                        decoration: BoxDecoration(
                                          color: ColorConstants
                                              .grayBackGroundColor,
                                          borderRadius: BorderRadius.circular(
                                              AppInsets.s4),
                                        ),
                                        child: UniversalImage(
                                          SvgsConstants.document,
                                          height: AppInsets.s24,
                                          width: AppInsets.s24,
                                        ),
                                      ),
                                      const SizedBox(height: AppInsets.s8),
                                      Text(
                                        documents[index].docName.toUpperCase(),
                                        style: TextStyle(
                                          fontSize: AppInsets.s16,
                                          color: ColorConstants.black,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: AppFonts.lexend,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(height: AppInsets.s2),
                                      Text(
                                        ((documents[index]
                                                    .fieldData
                                                    ?.firstWhereOrNull((field) =>
                                                        field.title
                                                            .toLowerCase() ==
                                                        'expiry date')
                                                    ?.value) as Timestamp?)
                                                ?.toDate()
                                                .goodDayDate() ??
                                            '-',
                                        style: TextStyle(
                                          fontSize: AppInsets.s12,
                                          color: ColorConstants.black,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: AppFonts.lexend,
                                        ),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              Positioned(
                                top: 0,
                                right: 0,
                                child: PopupMenuButton<String>(
                                  onSelected: (value) {
                                    if (value == 'edit') {
                                      editDocument(
                                          context,
                                          documents[index].docName,
                                          documents[index].documentId,
                                          documents[index].fieldData!);
                                    } else if (value == 'delete') {
                                      deleteVehicleDocumentById(
                                          vehicleDocId:
                                              widget.vehicleModel.docId,
                                          documentId:
                                              documents[index].documentId);
                                    } else if (value == 'download') {
                                      for (var element
                                          in documents[index].files) {
                                        _launchUrl(element.fileUrl);
                                      }
                                      // _launchUrl(documents[index].fileUrl);
                                    } else if (value == 'view') {
                                      showFilePreviewDialog(
                                        context,
                                        documents[index].files,
                                        // documents[index].fileUrl,
                                        // documents[index].fileType
                                      );
                                    } else if (value == 'copy') {
                                      // Clipboard.setData(ClipboardData(
                                      //         text: documents[index].fileUrl))
                                      //     .then((_) {
                                      //   // Show a confirmation message after copying
                                      //   ScaffoldMessenger.of(context)
                                      //       .showSnackBar(
                                      //     SnackBar(
                                      //         content: Text(
                                      //             'Link copied to clipboard!')),
                                      //   );
                                      // });
                                    }
                                  },
                                  itemBuilder: (BuildContext context) =>
                                      <PopupMenuEntry<String>>[
                                    const PopupMenuItem<String>(
                                      value: 'edit',
                                      child: Text('Edit'),
                                    ),
                                    if (documents[index].files.isNotEmpty) ...[
                                      const PopupMenuItem<String>(
                                        value: 'download',
                                        child: Text('Download'),
                                      ),
                                      const PopupMenuItem<String>(
                                        value: 'view',
                                        child: Text('View'),
                                      ),
                                      // const PopupMenuItem<String>(
                                      //   value: 'copy',
                                      //   child: Text('Copy Link'),
                                      // ),
                                    ],
                                    const PopupMenuItem<String>(
                                      value: 'delete',
                                      child: Text('Delete'),
                                    ),
                                  ],
                                  icon: const Icon(
                                    Icons.more_vert,
                                    color: primaryColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                        // }),
                        // );
                      }),
                    );
                  });
                } else {
                  return Center(child: CircularProgressIndicator());
                }
              }),
        ],
      ),
    );
  }

  void showFilePreviewDialog(BuildContext context, List<DocFiles> files) {
    int currentIndex = 0;
    List<DocFiles> displayData = [];
    for (var file in files) {
      displayData.addIf(file.fileUrl != '', file);
    }
    showDialog(
      context: context,
      builder: (dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            constraints: const BoxConstraints(
              maxHeight: 600,
              maxWidth: 500,
            ), // Optional: Prevent overflows on small screens
            child:
                // Column(
                //   mainAxisSize: MainAxisSize.min,
                //   crossAxisAlignment: CrossAxisAlignment.center,
                //   mainAxisAlignment: MainAxisAlignment.center,
                //   children: [
                //     Row(
                //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //       children: [
                //         Text(
                //           'Preview',
                //           style: Theme.of(context).textTheme.titleLarge,
                //         ),
                //         IconButton(
                //             onPressed: () {
                //               Clipboard.setData(ClipboardData(
                //                       text: files[currentIndex].fileUrl))
                //                   .then((_) {
                //                 // Show a confirmation message after copying
                //                 ScaffoldMessenger.of(context).showSnackBar(
                //                   SnackBar(
                //                       content: Text('Link copied to clipboard!')),
                //                 );
                //               });
                //             },
                //             icon: Icon(Icons.copy))
                //       ],
                //     ),
                //     const SizedBox(height: 16),
                //     CarouselSlider(
                //       options: CarouselOptions(
                //         enableInfiniteScroll: false,
                //         aspectRatio: 1,
                //         onPageChanged: (index, reason) {
                //           currentIndex = index;
                //         },
                //       ),
                //       items: [
                //         ...List.generate(
                //           displayData.length,
                //           (index) {
                //             print(displayData[index].fileUrl);
                //             print(displayData[index].fileType);
                //             return SizedBox(
                //                 height: 300,
                //                 width: 300,
                //                 child: displayData[index].fileType == 'image'
                //                     ? UniversalImage(
                //                         displayData[index].fileUrl,
                //                         fit: BoxFit.contain,
                //                       )
                //                     : SfPdfViewer.network(
                //                         displayData[index].fileUrl));
                //           },
                //         ),
                //       ],
                //     ),
                //     const SizedBox(height: 12),
                //     Align(
                //         alignment: Alignment.centerRight,
                //         child: TextButton(
                //           onPressed: () => Navigator.of(dialogContext).pop(),
                //           child: const Text("Close"),
                //         ))
                //   ],
                // ),
                Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Preview',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    Spacer(),
                    IconButton(
                        onPressed: () {
                          Clipboard.setData(ClipboardData(
                                  text: files[currentIndex].fileUrl))
                              .then((_) {
                            // Show a confirmation message after copying
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text('Link copied to clipboard!')),
                            );
                          });
                        },
                        icon: Icon(Icons.copy)),
                    IconButton(
                        onPressed: () {
                          launchUrl(Uri.parse(files[currentIndex].fileUrl));
                        },
                        icon: Icon(Icons.download))
                  ],
                ),
                const SizedBox(height: 16),
                CarouselSlider(
                  options: CarouselOptions(
                    enableInfiniteScroll: false,
                    aspectRatio: 1,
                    onPageChanged: (index, reason) {
                      currentIndex = index;
                    },
                  ),
                  items: [
                    ...List.generate(
                      files.length,
                      (index) {
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: files[index].fileType == 'image'
                              ? UniversalImage(
                                  files[index].fileUrl,
                                  fit: BoxFit.contain,
                                )
                              : true
                                  ? InkWell(
                                      hoverColor: Colors.transparent,
                                      splashColor: Colors.transparent,
                                      onTap: () async {
                                        final pdfUrl = files[index].fileUrl;
                                        final googleDocsUrl =
                                            'https://docs.google.com/gview?embedded=true&url=${Uri.encodeComponent(pdfUrl)}';

                                        final uri = Uri.parse(googleDocsUrl);

                                        if (await canLaunchUrl(uri)) {
                                          await launchUrl(uri,
                                              mode: LaunchMode
                                                  .externalApplication);
                                        } else {
                                          throw 'Could not launch PDF viewer';
                                        }
                                        context.pop();
                                      },
                                      child: SizedBox(
                                          height: 25,
                                          width: 25,
                                          child: Image.asset(
                                              'assets/images/share.png')),
                                    )
                                  : SfPdfViewer.network(files[index].fileUrl),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () => Navigator.of(dialogContext).pop(),
                    child: const Text("Close"),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> showTaskSelectionDialog(BuildContext context) async {
    List<String> taskNames = [];
    String? selectedTaskName;
    String? errorText;

    try {
      final taskCollection =
          await FBFireStore.tasks.where('isVehicle', isEqualTo: true).get();

      taskNames = taskCollection.docs
          .map((doc) => TaskModel.fromSnap(doc).taskName.toUpperCase())
          .toList();
    } catch (e) {
      debugPrint("Error loading task names: $e");
    }
    final dropdownController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            title: const Text('Document Type'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DropdownMenu<String>(
                  enableSearch: true,
                  requestFocusOnTap: false,
                  controller: dropdownController,
                  width: 280,
                  onSelected: (String? newTaskName) {
                    setState(() {
                      selectedTaskName = newTaskName;
                      errorText = null; // Clear error on selection
                    });
                  },
                  dropdownMenuEntries: taskNames.map((taskName) {
                    return DropdownMenuEntry<String>(
                      value: taskName,
                      label: taskName.toUpperCase(),
                    );
                  }).toList(),
                ),
                if (errorText != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      errorText!,
                      style: const TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  if (selectedTaskName == null || selectedTaskName!.isEmpty) {
                    setState(() {
                      errorText = 'Please select a document type.';
                    });
                  } else {
                    Navigator.of(context).pop();
                    showTaskDetailsDialog(context, selectedTaskName!);
                  }
                },
                child: const Text('Save'),
              ),
            ],
          );
        });
      },
    );
  }

  Future<void> showTaskDetailsDialog(
      BuildContext context, String taskName) async {
    try {
      List<SelectedFile?> selectedFile = [];
      bool loading = false;

      final Map<String, TextEditingController> controllers = {};
      final Map<String, String?> errors = {}; // ❗ Validation errors

      final task = taskModels.firstWhere(
        (taskModel) =>
            taskModel.taskName.toLowerCase() == taskName.toLowerCase(),
      );

      for (var detail in task.taskDetails) {
        controllers[detail.title] = TextEditingController(text: "");
        errors[detail.title] = null; // Init error map
      }

      showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState2) {
              return AlertDialog(
                title: const Text('Add Documents'),
                content: SizedBox(
                  width: 280,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    spacing: 20,
                    children: [
                      ...task.taskDetails.map((taskDetail) {
                        final controller = controllers[taskDetail.title]!;
                        final errorText = errors[taskDetail.title];

                        switch (taskDetail.type.toLowerCase()) {
                          case 'textfield':
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextFormField(
                                  controller: controller,
                                  onChanged: (newValue) {
                                    taskDetail.value = newValue;
                                    if (newValue.trim().isNotEmpty) {
                                      setState2(() {
                                        errors[taskDetail.title] = null;
                                      });
                                    }
                                  },
                                  cursorHeight: 20,
                                  decoration: inpDecor().copyWith(
                                    labelText:
                                        'Enter value for ${taskDetail.title}',
                                    errorText: errorText,
                                  ),
                                ),
                              ],
                            );
                          case 'datepicker':
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextFormField(
                                  controller: controller,
                                  decoration: inpDecor().copyWith(
                                    labelText:
                                        'Select a date for ${taskDetail.title}',
                                    errorText: errorText,
                                  ),
                                  onTap: () async {
                                    FocusScope.of(context).requestFocus(
                                        FocusNode()); // Dismiss keyboard
                                    DateTime? selectedDate =
                                        await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime(2000),
                                      lastDate: DateTime(2101),
                                    );
                                    if (selectedDate != null) {
                                      final formatted =
                                          "${selectedDate.toLocal()}"
                                              .split(' ')[0];
                                      controller.text = formatted;
                                      taskDetail.value = formatted;
                                      setState2(() {
                                        errors[taskDetail.title] = null;
                                      });
                                    }
                                  },
                                  readOnly: true,
                                ),
                              ],
                            );
                          case 'expirydate':
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // TextFormField(
                                //   controller: controller,
                                //   decoration: inpDecor().copyWith(
                                //     labelText:
                                //         'Select a date for ${taskDetail.title}',
                                //     errorText: errorText,
                                //   ),
                                //   onTap: () async {
                                //     FocusScope.of(context).requestFocus(
                                //         FocusNode()); // Dismiss keyboard
                                //     DateTime? selectedDate =
                                //         await showDatePicker(
                                //       context: context,
                                //       initialDate: DateTime.now(),
                                //       firstDate: DateTime(2000),
                                //       lastDate: DateTime(2101),
                                //     );
                                //     if (selectedDate != null) {
                                //       final formatted =
                                //           "${selectedDate.toLocal()}"
                                //               .split(' ')[0];
                                //       controller.text = formatted;
                                //       taskDetail.value = selectedDate;
                                //       setState2(() {
                                //         errors[taskDetail.title] = null;
                                //       });
                                //     }
                                //   },
                                //   readOnly: true,
                                // ),
                                DateFormatField(
                                    decoration: inpDecor().copyWith(
                                      labelText:
                                          'Enter a date for ${(taskDetail.title)}',
                                      // errorText: errorText,
                                    ),
                                    type: DateFormatType.type1,
                                    onComplete: (date) {
                                      print(date.toString);
                                      if (date != null) {
                                        final formatted =
                                            "${date.toLocal()}".split(' ')[0];
                                        controller.text = formatted;
                                        taskDetail.value = date;
                                        setState2(() {
                                          // errors[taskDetail.title] = null;
                                        });
                                      }
                                    }),
                                const SizedBox(height: 10),
                              ],
                            );
                          default:
                            return const SizedBox();
                        }
                      }),
                      InkWell(
                        highlightColor: Colors.transparent,
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        hoverColor: Colors.transparent,
                        onTap: () async {
                          try {
                            final fileUrl = await ImagePickerService()
                                .pickFile(context, true);

                            if (fileUrl.isNotEmpty) {
                              selectedFile.addAll(fileUrl);
                            }
                            setState2(() {});
                          } catch (e) {
                            debugPrint("Failed to pick doc: $e");
                            setState2(() {
                              loading = false;
                            });
                          }
                        },
                        child: Container(
                            padding: EdgeInsets.all(10),
                            width: double.maxFinite,
                            height: 150,
                            clipBehavior: Clip.antiAlias,
                            decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey.shade400),
                                borderRadius: BorderRadius.circular(7),
                                // color: const Color.fromARGB(9, 0, 0, 0),
                                color: Colors.transparent),
                            child: Wrap(
                              spacing: 10,
                              runSpacing: 10,
                              children: [
                                ...List.generate(
                                  selectedFile.length,
                                  (index) {
                                    return selectedFile[index] == null
                                        ? const Icon(Icons.file_copy_outlined)
                                        : selectedFile[index]!.type == 'image'
                                            ? SizedBox(
                                                height: 50,
                                                width: 50,
                                                child: Image.memory(
                                                  selectedFile[index]!
                                                      .uInt8List,
                                                  width: double.maxFinite,
                                                  fit: BoxFit.cover,
                                                ),
                                              )
                                            : Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                spacing: 8,
                                                children: [
                                                  const Icon(
                                                      Icons.picture_as_pdf),
                                                  Text(
                                                      selectedFile[index]!.name)
                                                ],
                                              );
                                  },
                                )
                              ],
                            )),
                      ),
                      // if (selectedFile == null)
                      //   const Padding(
                      //     padding: EdgeInsets.only(top: 8),
                      //     child: Text(
                      //       "Please attach a file.",
                      //       style: TextStyle(color: Colors.red, fontSize: 12),
                      //     ),
                      //   ),
                    ],
                  ),
                ),
                actions: loading
                    ? [CircularProgressIndicator()]
                    : [
                        TextButton(
                          onPressed: () async {
                            // ❗ Validate before submission
                            bool hasError = false;

                            for (var detail in task.taskDetails) {
                              final value = (detail.type == "ExpiryDate")
                                  ? Timestamp.fromDate(detail.value)
                                  : controllers[detail.title]?.text.trim();
                              if (value == null) {
                                errors[detail.title] = 'This field is required';
                                hasError = true;
                              } else {
                                errors[detail.title] = null;
                                detail.value = value;
                              }
                            }

                            // if (selectedFile == null) {
                            //   hasError = true;
                            // }
                            loading = true;
                            setState2(() {});

                            if (hasError) return;

                            try {
                              setState2(() {
                                loading = true;
                              });

                              // Upload file if selected
                              // final fileUrl = selectedFile != null
                              //     ? await uploadUserDocs(selectedFile!)
                              //     : '';
                              // final uuid = Uuid();
                              // final documentId = uuid.v4();
                              Map fielddata = {};
                              for (var element in task.taskDetails) {
                                fielddata.addEntries(element.toJson().entries);
                              }
                              List<Map> files = [];
                              for (var element in selectedFile) {
                                final fileUrl = element != null
                                    ? await uploadUserDocs(element)
                                    : "";
                                files.add({element?.type: fileUrl});
                              }
                              UserModel? user = Get.find<HomeCtrl>()
                                  .users
                                  .firstWhereOrNull((element) =>
                                      element.docId ==
                                      widget.vehicleModel.userId);
                              final Map<String, dynamic> newTaskDoc = {
                                // 'documentId': documentId,
                                'docName': task.taskName,
                                'files': files,
                                'uId': widget.vehicleModel.userId,
                                'vehicleId': widget.vehicleModel.docId,
                                'vehicleDoc': true,
                                'isArchived': false,
                                // 'fileType':
                                //     selectedFile != null ? selectedFile!.type : "",
                                'fieldData': fielddata,
                                "userName": user?.name,
                                "userContact": user?.number,
                                "vehicleNum": widget.vehicleModel.vehicleNumber,
                                // task.taskDetails.map((e) => e.toJson()).toList(),
                              };

                              // Push the task doc to the selected vehicle document
                              // await FBFireStore.vehicles
                              //     .doc(widget.vehicleModel.docId)
                              //     .update({
                              //   'vehicleDocuments':
                              //       FieldValue.arrayUnion([newTaskDoc]),
                              // });
                              await FBFireStore.usersDocs.add(newTaskDoc);
                              setState2(() {
                                loading = false;
                              });

                              if (context.mounted) {
                                Navigator.of(context).pop(); // Close the dialog
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text("Document added to vehicle."),
                                  ),
                                );
                              }
                            } catch (e) {
                              debugPrint("Error saving document: $e");
                              setState2(() {
                                loading = false;
                              });
                            }
                          },
                          child: const Text("Add"),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: const Text("Cancel"),
                        ),
                      ],
              );
            },
          );
        },
      );
    } catch (e) {
      debugPrint("Error loading task details: $e");
    }
  }

  Future<void> editDocument(
    BuildContext context,
    String taskName,
    String documentId,
    List<TaskDetailModel> taskDetails,
  ) async {
    try {
      List<SelectedFile?> selectedFile = [];
      bool loading = false;
      final Map<String, TextEditingController> controllers = {};

      // Initialize controllers with values from the task details
      for (var detail in taskDetails) {
        controllers[detail.title] = (detail.value is Timestamp)
            ? TextEditingController(
                text: (detail.value as Timestamp).toDate().convertToYYYYMMDD())
            : TextEditingController(text: detail.value);
      }

      showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState2) {
              return AlertDialog(
                title: Text('Add Documents for $taskName'),
                content: SizedBox(
                  width: 280,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ...taskDetails.map((taskDetail) {
                        final controller = controllers[taskDetail.title]!;

                        switch (taskDetail.type.toLowerCase()) {
                          case 'textfield':
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: TextFormField(
                                controller: controller,
                                onChanged: (newValue) {
                                  taskDetail.value = newValue;
                                },
                                cursorHeight: 20,
                                decoration: inpDecor().copyWith(
                                  labelText:
                                      'Enter value for ${taskDetail.title}',
                                ),
                              ),
                            );
                          case 'datepicker':
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: TextFormField(
                                controller: controller,
                                decoration: inpDecor().copyWith(
                                  labelText:
                                      'Select a date for ${taskDetail.title}',
                                ),
                                onTap: () async {
                                  DateTime? selectedDate = await showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now(),
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime(2101),
                                  );
                                  if (selectedDate != null) {
                                    final formatted =
                                        "${selectedDate.toLocal()}"
                                            .split(' ')[0];
                                    controller.text = formatted;
                                    taskDetail.value = formatted;
                                  }
                                },
                                readOnly: true,
                              ),
                            );
                          case 'expirydate':
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // TextFormField(
                                //   controller: controller,
                                //   decoration: inpDecor().copyWith(
                                //     labelText:
                                //         'Select a date for ${(taskDetail.title)}',
                                //     // errorText: errorText,
                                //   ),
                                //   onTap: () async {
                                //     FocusScope.of(context).requestFocus(
                                //         FocusNode()); // Dismiss keyboard
                                //     DateTime? selectedDate =
                                //         await showDatePicker(
                                //       context: context,
                                //       initialDate: DateTime.now(),
                                //       firstDate: DateTime(2000),
                                //       lastDate: DateTime(2101),
                                //     );
                                //     if (selectedDate != null) {
                                //       final formatted =
                                //           "${selectedDate.toLocal()}"
                                //               .split(' ')[0];
                                //       controller.text = formatted;
                                //       taskDetail.value = selectedDate;
                                //       setState2(() {
                                //         // errors[taskDetail.title] = null;
                                //       });
                                //     }
                                //   },
                                //   readOnly: true,
                                // ),
                                DateFormatField(
                                    controller: controller,
                                    decoration: inpDecor().copyWith(
                                      labelText:
                                          'Enter a date for ${(taskDetail.title)}',
                                      // errorText: errorText,
                                    ),
                                    type: DateFormatType.type1,
                                    onComplete: (date) {
                                      print(date.toString);
                                      if (date != null) {
                                        final formatted =
                                            "${date.toLocal()}".split(' ')[0];
                                        controller.text = formatted;
                                        taskDetail.value = date;
                                        setState2(() {
                                          // errors[taskDetail.title] = null;
                                        });
                                      }
                                    }),
                                const SizedBox(height: 10),
                              ],
                            );

                          default:
                            return const SizedBox.shrink();
                        }
                      }),
                      const SizedBox(height: 16),
                      InkWell(
                        highlightColor: Colors.transparent,
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        hoverColor: Colors.transparent,
                        onTap: () async {
                          try {
                            final file = await ImagePickerService()
                                .pickFile(context, true);
                            if (file.isNotEmpty) {
                              selectedFile.addAll(file);
                            }
                            setState2(() {});
                          } catch (e) {
                            debugPrint("Failed to pick doc: $e");
                            setState2(() {
                              loading = false;
                            });
                          }
                        },
                        child: Container(
                          width: double.infinity,
                          height: 150,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(7),
                            color: Colors.transparent,
                          ),
                          child: Wrap(
                            spacing: 10,
                            runSpacing: 10,
                            children: [
                              ...List.generate(
                                selectedFile.length,
                                (index) {
                                  return selectedFile[index] == null
                                      ? const Center(
                                          child: Icon(Icons.file_copy_outlined))
                                      : selectedFile[index]?.type == 'image'
                                          ? SizedBox(
                                              height: 50,
                                              width: 50,
                                              child: Image.memory(
                                                selectedFile[index]!.uInt8List,
                                                width: double.maxFinite,
                                                fit: BoxFit.cover,
                                              ),
                                            )
                                          : Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              spacing: 8,
                                              children: [
                                                const Icon(
                                                    Icons.picture_as_pdf),
                                                Text(
                                                    selectedFile[index]?.name ??
                                                        "")
                                              ],
                                            );
                                },
                              ),
                              SizedBox(
                                  height: 50,
                                  width: 50,
                                  child: Icon(Icons.add, color: Colors.black))
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                actions: loading
                    ? const [
                        Center(
                          child: SizedBox(
                            height: 25,
                            width: 25,
                            child: CircularProgressIndicator(strokeWidth: 2.5),
                          ),
                        )
                      ]
                    : [
                        TextButton(
                          onPressed: () async {
                            try {
                              setState2(() {
                                loading = true;
                              });

                              // final fileUrl = selectedFile != null
                              //     ? await uploadUserDocs(selectedFile!)
                              //     : "";
                              //
                              // final uuid = Uuid();
                              // final documentId = uuid.v4();
                              // final Map<String, dynamic> newTaskDoc = {
                              //   'documentId': documentId,
                              //   'fileType': selectedFile?.type ?? '',
                              //   'fileUrl': fileUrl,
                              //   'fieldData':
                              //       taskDetails.map((e) => e.toJson()).toList(),
                              // };

                              // final userDocRef = FBFireStore.vehicles
                              //     .doc(widget.vehicleModel.userId);
                              final userDocRef =
                                  FBFireStore.usersDocs.doc(documentId);

// 1. Get the user's existing document
                              final userDoc = await userDocRef.get();
                              // List<dynamic> existingDocs =
                              //     userDoc.data()?['vehicleDocuments'] ?? [];

// 2. Check if a document for this task already exists
                              // final docIndex = existingDocs.indexWhere(
                              //     (doc) => doc['docName'] == taskName);
                              Map fieldData = {};
                              for (var detail in taskDetails) {
                                fieldData.addEntries(detail.toJson().entries);
                              }
                              List<Map> files = [];
                              for (var element in selectedFile) {
                                final fileUrl = element != null
                                    ? await uploadUserDocs(element)
                                    : "";
                                files.add({element?.type: fileUrl});
                              }
                              // final newFieldData =
                              //     taskDetails.map((e) => e.toJson()).toList();

                              // if (docIndex >= 0) {
                              // 3. Update the specific document's fields
                              final existingDoc = userDoc.data() ?? {};
                              existingDoc['fieldData'] = fieldData;
                              existingDoc['fields'] = files;
                              // existingDoc['fileType'] =
                              //     selectedFile?.type ?? '';
                              // existingDoc['fileUrl'] = selectedFile != null
                              // ? await uploadUserDocs(selectedFile!)
                              // : '';

                              // existingDocs[docIndex] = existingDoc;
                              // } else {
                              // final uuid = Uuid();
                              // final documentId = uuid.v4();
                              // final Map<String, dynamic> newTaskDoc = {
                              //   'documentId': documentId,
                              //   'docName': taskName,
                              //   'fileType': selectedFile?.type ?? '',
                              //   'fileUrl': selectedFile != null
                              //       ? await uploadUserDocs(selectedFile!)
                              //       : '',
                              //   "isDeleted": false,
                              //   'fieldData': newFieldData,
                              // };
                              // existingDocs.add(newTaskDoc);
                              // }

// 5. Update the entire `documents` array
                              await userDocRef.update(existingDoc);

                              if (context.mounted) {
                                Navigator.of(context).pop();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text("Document added"),
                                  ),
                                );
                              }
                            } catch (e) {
                              debugPrint("Error saving document: $e");
                            } finally {
                              setState2(() {
                                loading = false;
                              });
                            }
                          },
                          child: const Text("Add"),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text("Cancel"),
                        ),
                      ],
              );
            },
          );
        },
      );
    } catch (e) {
      debugPrint("Error loading task details: $e");
    }
  }

  Future<void> deleteVehicleDocumentById({
    required String vehicleDocId,
    required String documentId,
  }) async {
    debugPrint("Document ID to delete: $documentId");

    try {
      // Fetch the vehicle document
      final vehicleDocSnapshot =
          await FBFireStore.vehicles.doc(vehicleDocId).get();

      if (vehicleDocSnapshot.exists) {
        // final vehicleDoc = vehicleDocSnapshot.data();
        // final List<dynamic> documents = vehicleDoc?['vehicleDocuments'] ?? [];

        // Find the document with the matching documentId
        // final documentToDelete = documents.firstWhere(
        //   (doc) => doc['documentId'] == documentId,
        //   orElse: () => null,
        // );

        // if (documentToDelete != null) {
        //   // Remove the exact matching document
        //   await FBFireStore.vehicles.doc(vehicleDocId).update({
        //     'vehicleDocuments': FieldValue.arrayRemove([documentToDelete]),
        //   });
        await FBFireStore.usersDocs.doc(documentId).delete();
        debugPrint("Document deleted successfully.");
        // } else {
        //   debugPrint("Document with ID $documentId not found.");
        // }
      } else {
        debugPrint("Vehicle document not found.");
      }
    } catch (error) {
      debugPrint("Error deleting vehicle document: $error");
    }
  }

  Future<void> _launchUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.inAppWebView,
        webOnlyWindowName: '_blank',
      );
    } else {
      throw 'Could not launch $url';
    }
  }
}
