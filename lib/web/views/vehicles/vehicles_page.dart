import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/web/shared/const.dart';
import 'package:mass_ibs/web/views/common/delete_alert.dart';
import 'package:mass_ibs/web/views/users/users_page.dart';

import '../../../modules/add_vehicle/add_vehicle_controller.dart';
import '../../../shared/constants/colors.dart';
import '../../../shared/constants/svgs_constants.dart';
import '../../../shared/firebase.dart';
import '../../../shared/widgets/universal_image.dart';
import '../../controller/home_controller.dart';
import '../../models/vehicle_model.dart';
import '../../shared/methods.dart';
import '../../shared/router.dart';
import '../../shared/theme.dart';
import '../common/header_search_feild.dart';
import '../common/page_header.dart';

class VehiclesPage extends StatefulWidget {
  const VehiclesPage({super.key});

  @override
  State<VehiclesPage> createState() => _VehiclesPageState();
}

class _VehiclesPageState extends State<VehiclesPage> {
  bool loading = true;
  int vehicleDocCount = 0;
  int currentPage = 1;
  int totalPages = 1;

  void goToPrevious() {
    if (currentPage > 1) {
      setState(() => currentPage--);
    }
  }

  void goToNext() {
    if (currentPage < totalPages) {
      setState(() => currentPage++);
    }
  }

  final searchController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: GetBuilder<HomeCtrl>(builder: (homeController) {
        List<VehicleModel> vehiclesList = homeController.vehicles
            .where((element) =>
                element.vehicleNumber
                    .toLowerCase()
                    .contains(searchController.text.toLowerCase()) ||
                element.vehicleName
                    .toLowerCase()
                    .contains(searchController.text.toLowerCase()) ||
                element.chassisNumber
                    .toLowerCase()
                    .contains(searchController.text.toLowerCase()))
            .toList();
        totalPages = (vehiclesList.length / vehiclePerPageLimit).ceil();
        final paginatedList = vehiclesList.sublist(
            (currentPage - 1) * vehiclePerPageLimit,
            ((currentPage) * vehiclePerPageLimit) > vehiclesList.length
                ? vehiclesList.length
                : (currentPage) * vehiclePerPageLimit);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PageHeaderWithButton(
              title: "Vehicles",
              onPressed: () {},
              buttonName: 'New',
              count: "(${Get.find<HomeCtrl>().vehicles.length})",
              button: false,
              icon: CupertinoIcons.add,
            ),
            const SizedBox(height: 20),
            SearchField(
              searchController: searchController,
              onChanged: (p0) {
                setState(() {});
              },
            ),
            const SizedBox(height: 20),
            Wrap(
              spacing: AppInsets.s16,
              runSpacing: AppInsets.s16,
              children: List.generate(paginatedList.length, (index) {
                // vehicleDocs(vehiclesList[index].docId);
                return Stack(
                  clipBehavior: Clip.antiAliasWithSaveLayer,
                  children: [
                    InkWell(
                      highlightColor: Colors.transparent,
                      overlayColor:
                          const WidgetStatePropertyAll(Colors.transparent),
                      hoverColor: Colors.transparent,
                      onTap: () {
                        context.push(
                          '${WebRoutes.vehicle}/${paginatedList[index].docId}',
                          extra: paginatedList[index],
                        );
                      },
                      child: Container(
                        constraints: const BoxConstraints(
                          maxWidth: 200,
                          minWidth: 144,
                          minHeight: 128,
                          maxHeight: 128,
                        ),
                        padding:
                            const EdgeInsets.symmetric(vertical: AppInsets.s12),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          border: Border.all(
                            color: ColorConstants.borderColor,
                            width: AppInsets.s1,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: AppInsets.pageMargin),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(AppInsets.s6),
                                width: AppInsets.s36,
                                height: AppInsets.s36,
                                decoration: BoxDecoration(
                                  color: ColorConstants.grayBackGroundColor,
                                  borderRadius:
                                      BorderRadius.circular(AppInsets.s4),
                                ),
                                child: UniversalImage(
                                  SvgsConstants.car,
                                  height: AppInsets.s24,
                                  width: AppInsets.s24,
                                ),
                              ),
                              const SizedBox(height: AppInsets.s8),
                              Text(
                                paginatedList[index]
                                    .vehicleNumber
                                    .toUpperCase(),
                                style: TextStyle(
                                  fontSize: AppInsets.s16,
                                  color: ColorConstants.black,
                                  fontWeight: FontWeight.w500,
                                  fontFamily: AppFonts.lexend,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                              // Text(
                              //   'Documents: $vehicleDocCount',
                              //   // 'Documents: ${paginatedList[index].vehicleDocuments.length}',
                              //   style: TextStyle(
                              //     fontSize: AppInsets.s14,
                              //     color: ColorConstants.textFieldHintColor,
                              //     fontWeight: FontWeight.w400,
                              //     fontFamily: AppFonts.lexend,
                              //   ),
                              //   overflow: TextOverflow.ellipsis,
                              // ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 0,
                      right: 0,
                      child: PopupMenuButton<String>(
                        onSelected: (value) {
                          if (value == 'edit') {
                            addVehicleForm(
                              context,
                              paginatedList[index],
                              paginatedList[index].userId,
                            );
                          } else if (value == 'delete') {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return DeleteAlert(
                                  yesPressed: () async {
                                    try {
                                      await FBFireStore.vehicles
                                          .doc(paginatedList[index].docId)
                                          .delete();
                                      await FBFireStore.usersDocs
                                          .where('vehicleId',
                                              isEqualTo:
                                                  paginatedList[index].docId)
                                          .get()
                                          .then((value) async {
                                        for (var element in value.docs) {
                                          await FBFireStore.usersDocs
                                              .doc(element.id)
                                              .delete();
                                        }
                                      });
                                    } on Exception catch (e) {
                                      debugPrint(e.toString());
                                    }
                                  },
                                );
                              },
                            );
                          }
                        },
                        itemBuilder: (BuildContext context) =>
                            <PopupMenuEntry<String>>[
                          const PopupMenuItem<String>(
                            value: 'edit',
                            child: Text('Edit'),
                          ),
                          const PopupMenuItem<String>(
                            value: 'delete',
                            child: Text('Delete'),
                          ),
                        ],
                        icon: const Icon(
                          Icons.more_vert,
                          color: primaryColor,
                        ),
                      ),
                    ),
                  ],
                );
              }),

              //[
              // ListView.builder(
              //   shrinkWrap: true,
              //   itemCount: vehiclesList.length,
              //   itemBuilder: (context, index) {
              //     return InkWell(
              //       onTap: () {
              //         // TODO:
              //       },
              //       child: Container(
              //         padding:
              //             const EdgeInsets.symmetric(vertical: AppInsets.s12),
              //         decoration: BoxDecoration(
              //           color: Colors.white,
              //           borderRadius: BorderRadius.circular(AppInsets.s4),
              //           border: Border.all(
              //             color: ColorConstants.borderColor,
              //             width: AppInsets.s1,
              //           ),
              //         ),
              //         child: Padding(
              //           padding: const EdgeInsets.symmetric(
              //               horizontal: AppInsets.pageMargin),
              //           child: Column(
              //             crossAxisAlignment: CrossAxisAlignment.start,
              //             children: [
              //               Container(
              //                 padding: const EdgeInsets.all(AppInsets.s6),
              //                 width: AppInsets.s36,
              //                 height: AppInsets.s36,
              //                 decoration: BoxDecoration(
              //                   color: ColorConstants.grayBackGroundColor,
              //                   borderRadius: BorderRadius.circular(AppInsets.s4),
              //                 ),
              //                 child: UniversalImage(
              //                   SvgsConstants.car,
              //                   height: AppInsets.s24,
              //                   width: AppInsets.s24,
              //                 ),
              //               ),
              //               const SizedBox(height: AppInsets.s8),
              //               Text(
              //                 vehiclesList[index].vehicleNumber.toUpperCase(),
              //                 style: TextStyle(
              //                   fontSize: AppInsets.s16,
              //                   color: ColorConstants.black,
              //                   fontWeight: FontWeight.w500,
              //                   fontFamily: AppFonts.lexend,
              //                 ),
              //                 overflow: TextOverflow.ellipsis,
              //               ),
              //               Text(
              //                 'upload document: 3',
              //                 style: TextStyle(
              //                   fontSize: AppInsets.s14,
              //                   color: ColorConstants.textFieldHintColor,
              //                   fontWeight: FontWeight.w400,
              //                   fontFamily: AppFonts.lexend,
              //                 ),
              //                 overflow: TextOverflow.ellipsis,
              //               ),
              //             ],
              //           ),
              //         ),
              //       ),
              //     );
              //   },
              // ),
              //],
            ),
            SizedBox(
              height: 20,
            ),
            Center(
                child: PaginationArrowButtons(
                    currentPage: currentPage,
                    totalPages: totalPages,
                    onPrevious: () {
                      goToPrevious();
                      setState(() {});
                    },
                    onNext: () {
                      goToNext();
                      setState(() {});
                    })),
          ],
        );
      }),
    );
  }

  Future<dynamic> addVehicleForm(
    BuildContext context,
    VehicleModel? vehicle,
    String userId,
  ) {
    bool loading = false;
    VehicleType selectedVehicleType;
    TextEditingController vehicleNameController = TextEditingController();
    TextEditingController vehicleNumberController = TextEditingController();
    TextEditingController chassisNumberController = TextEditingController();
    TextEditingController vehicleTypeController = TextEditingController();
    if (vehicle != null) {
      vehicleNameController.text = vehicle.vehicleName;
      vehicleNumberController.text = vehicle.vehicleNumber;
      chassisNumberController.text = vehicle.chassisNumber;
      vehicleTypeController.text = vehicle.vehicleType;
    }
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            contentPadding:
                const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
            title: Text(vehicle != null ? "Edit Vehicle" : "Add vehicle"),
            content: SizedBox(
              width: 280,
              child: Form(
                key: formKey, // 👈 Attach form key
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: vehicleNameController,
                      cursorHeight: 20,
                      decoration:
                          inpDecor().copyWith(labelText: 'Vehicle Name*'),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Vehicle name is required';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: vehicleNumberController,
                      cursorHeight: 20,
                      decoration:
                          inpDecor().copyWith(labelText: 'Vehicle Number*'),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Vehicle number is required';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: loading
                ? [
                    const Center(
                      child: SizedBox(
                        height: 25,
                        width: 25,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                        ),
                      ),
                    )
                  ]
                : [
                    TextButton(
                      onPressed: () async {
                        try {
                          if (!formKey.currentState!.validate()) {
                            return;
                          }

                          setState2(() {
                            loading = true;
                          });

                          final data = {
                            'userId': userId,
                            'vehicleName': vehicleNameController.text.trim(),
                            'chassisNumber':
                                chassisNumberController.text.trim(),
                            'vehicleNumber':
                                vehicleNumberController.text.trim(),
                            'vehicleType': vehicleTypeController.text.trim(),
                            "isDeleted": false,
                            'createdAt': vehicle != null
                                ? vehicle.createdAt
                                : FieldValue.serverTimestamp(),
                          };
                          if (vehicle != null) {
                            await updateVehicle(vehicle.docId, data);
                          } else {
                            // Add new vehicle
                            await addVehicle(data);
                          }
                          const snackBar = SnackBar(
                            content: Text("Vehicle Added"),
                            duration: Duration(seconds: 2),
                          );
                          if (context.mounted) {
                            vehicleNameController.clear();
                            vehicleNumberController.clear();
                            vehicleTypeController.clear();
                            chassisNumberController.clear();
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                          }
                        } catch (e, stack) {
                          debugPrint("❌ Error in user add/update: $e");
                          debugPrint("🧵 Stacktrace: $stack");
                          // Optionally show an error to the user
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text("Something went wrong: $e")),
                          );
                        } finally {
                          setState2(() {
                            loading = false;
                          });
                        }
                      },
                      child: Text(vehicle != null ? "Update" : "Add"),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text("Cancel"),
                    ),
                  ],
          );
        });
      },
    );
  }

  Future<void> updateVehicle(String docId, Map<String, dynamic> data) async {
    try {
      await FBFireStore.vehicles.doc(docId).update(data);
    } catch (e, stack) {
      debugPrint("🔥 Firestore Update Error: ${e.toString()}");
      debugPrint("Stack trace: $stack");
      rethrow;
    }
  }

  Future<void> addVehicle(Map<String, dynamic> data) async {
    try {
      await FBFireStore.vehicles.add(data);
    } catch (e, stack) {
      debugPrint("🔥 Critical Firestore Error: ${e.toString()}");
      debugPrint("Stack trace: $stack");
      rethrow;
    }
  }
}
