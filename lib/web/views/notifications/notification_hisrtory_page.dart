import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/shared/firebase.dart';
import 'package:mass_ibs/web/controller/home_controller.dart';
import 'package:mass_ibs/web/models/notification_model.dart';
import 'package:mass_ibs/web/shared/router.dart';
import 'package:mass_ibs/web/shared/theme.dart';
import 'package:mass_ibs/web/views/common/delete_alert.dart';
import 'package:mass_ibs/web/views/common/page_header.dart';

class NotiHistory extends StatelessWidget {
  const NotiHistory({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: primaryColor.withValues(alpha: 0.01),
      body: StreamBuilder(
          stream: FBFireStore.notifications.snapshots(),
          builder: (context, snapshot) {
            if (snapshot.hasError) {
              return Text("Facing some issue!!");
            } else if (snapshot.hasData) {
              List<NotificationModel> notifications = snapshot.data!.docs
                  .map((e) => NotificationModel.fromSnap(e))
                  .toList();
              return SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        IconButton(
                            onPressed: () {
                              context.go(WebRoutes.notifications);
                            },
                            icon: Icon(Icons.arrow_back)),
                        PageHeaderWithButton(
                          title: 'Notification History',
                          button: false,
                          onPressed: () {},
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    Container(
                        constraints: BoxConstraints(
                            // minWidth: MediaQuery.sizeOf(context).width / 2,
                            // maxWidth: MediaQuery.sizeOf(context).width / 2,
                            ),
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius:
                              BorderRadius.circular(12), // Rounded rectangle
                        ),
                        child: Column(
                          children: [
                            Container(
                              height: 50,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 5, vertical: 8.0),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 100,
                                      child: Center(
                                          child: Text(
                                        "Sr No.",
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold),
                                      )),
                                    ),
                                    Expanded(
                                        child: Text(
                                      "Title",
                                      style: TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )),
                                  ],
                                ),
                              ),
                            ),
                            ...List.generate(
                              notifications.length,
                              (index) {
                                return Container(
                                  height: 40,
                                  color: index % 2 == 0
                                      ? Colors.grey[200]
                                      : Colors.white,
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 5, vertical: 8.0),
                                    child: Row(
                                      children: [
                                        SizedBox(
                                            width: 100,
                                            child: Center(
                                                child: Text("${index + 1}"))),
                                        Expanded(
                                            child: Text(
                                                notifications[index].title)),
                                        IconButton(
                                            onPressed: () async {
                                              await showDialog(
                                                context: context,
                                                builder: (context) =>
                                                    DeleteAlert(
                                                        yesPressed: () async {
                                                  await FBFireStore
                                                      .notifications
                                                      .doc(notifications[index]
                                                          .docId)
                                                      .delete();
                                                  Navigator.of(context).pop();
                                                }),
                                              );
                                            },
                                            color: Colors.redAccent,
                                            icon: Icon(
                                              CupertinoIcons.delete,
                                              size: 18,
                                            ))
                                      ],
                                    ),
                                  ),
                                );
                              },
                            )
                          ],
                        )),
                  ],
                ),
              );
            } else {
              return Center(child: CircularProgressIndicator());
            }
          }),
    );
  }
}
