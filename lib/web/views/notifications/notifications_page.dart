import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/web/shared/router.dart';

import '../../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  TextEditingController titleCtrl = TextEditingController();
  TextEditingController descriptionCtrl = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: primaryColor.withValues(alpha: 0.01),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                PageHeaderWithButton(
                  title: 'Notifications',
                  button: false,
                  onPressed: () {},
                ),
                Spacer(),
                ElevatedButton.icon(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      elevation: 0,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    onPressed: () {
                      context.go(WebRoutes.notiHistory);
                    },
                    icon: const Icon(
                      Icons.history,
                      size: 20,
                    ),
                    label: Text("History"))
              ],
            ),
            const SizedBox(height: 20),
            Container(
              constraints: BoxConstraints(
                minWidth: MediaQuery.sizeOf(context).width / 2,
                maxWidth: MediaQuery.sizeOf(context).width / 2,
              ),
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12), // Rounded rectangle
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextFormField(
                      controller: titleCtrl,
                      cursorHeight: 20,
                      decoration: inpDecor().copyWith(labelText: 'Title'),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Title is required';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: descriptionCtrl,
                      cursorHeight: 20,
                      maxLines: 3,
                      decoration: inpDecor().copyWith(labelText: 'Description'),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Description is required';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton.icon(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: primaryColor,
                        elevation: 0,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      onPressed: () async {
                        if (_formKey.currentState?.validate() ?? false) {
                          final data = {
                            'desc': descriptionCtrl.text.trim(),
                            'title': titleCtrl.text.trim(),
                            'test': false,
                            'createdAt': FieldValue.serverTimestamp(),
                          };
                          await addNotification(data);
                          const snackBar = SnackBar(
                            content: Text("Notification sent"),
                            duration: Duration(seconds: 2),
                          );
                          titleCtrl.clear();
                          descriptionCtrl.clear();
                          if (context.mounted) {
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                          }
                        }
                      },
                      icon: const Icon(
                        Icons.notification_add,
                        size: 20,
                      ),
                      label: const Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                        child: Text("Send", style: TextStyle(fontSize: 14)),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Future<void> sendGlobalNotification({
  //   required String title,
  //   required String desc,
  // }) async {
  //   final userId = 'admin_or_any';
  //   await FirebaseFirestore.instance.collection('notifies').doc(userId).set({
  //     'title': title,
  //     'desc': desc,
  //     'timestamp': FieldValue.serverTimestamp(),
  //   });
  // }

  addNotification(Map<String, dynamic> data) async {
    try {
      await FBFireStore.notifications.add(data);
    } catch (e, stack) {
      debugPrint("🔥 Critical Firestore Error: ${e.toString()}");
      debugPrint("Stack trace: $stack");
      rethrow;
    }
  }
}
