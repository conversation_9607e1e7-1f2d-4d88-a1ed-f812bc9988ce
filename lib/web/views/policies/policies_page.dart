import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:date_format_field/date_format_field.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/web/models/user_docs_model.dart';
import 'package:mass_ibs/web/shared/const.dart';
import 'package:mass_ibs/web/views/common/table_header.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../shared/constants/colors.dart';
import '../../../shared/constants/svgs_constants.dart';
import '../../../shared/firebase.dart';
import '../../../shared/widgets/universal_image.dart';
import '../../controller/home_controller.dart';
import '../../models/task_model.dart';
import '../../models/user_model.dart';
import '../../models/vehicle_model.dart';
import '../../services/image_picker.dart';
import '../../shared/methods.dart';
import '../../shared/theme.dart';
import '../common/header_search_feild.dart';
import '../common/page_header.dart';

class PoliciesPage extends StatefulWidget {
  const PoliciesPage({
    super.key,
  });

  @override
  State<PoliciesPage> createState() => _PoliciesPageState();
}

class _PoliciesPageState extends State<PoliciesPage>
    with SingleTickerProviderStateMixin {
  TextEditingController searchController = TextEditingController();
  late TabController _tabController;
  // int _selectedTabIndex = 0;
  int? expiredCount;
  int? aboutToExpireCount;
  int currentPage = 1;

  DocumentSnapshot? lastDocument;
  List<DocumentSnapshot> documentStack = [];
  void goToNext() {
    if (lastDocument != null) {
      currentPage++;
      setState(() {
        documentStack.add(lastDocument!);
      });
    }
  }

  void goToPrevious() {
    if (documentStack.length > 1) {
      setState(() {
        currentPage--;

        documentStack.removeLast();
        lastDocument = documentStack.last;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    getCount();
    // _tabController.addListener(() {
    //   if (_tabController.indexIsChanging) return; // Prevent firing during swipe
    //   if (mounted) {
    //     setState(() {
    //       _selectedTabIndex = _tabController.index;
    //     });
    //   }
    // });
  }

  getCount() async {
    try {
      expiredCount = (await FBFireStore.usersDocs
                  .where('vehicleDoc', isEqualTo: true)
                  .where('isArchived', isEqualTo: false)
                  .where('docName', whereIn: [
                    'Vehicle Fitness',
                    'Insurance',
                    'Vehicle Permit',
                  ])
                  .where('fieldData.Expiry_Date.value',
                      isLessThan: Timestamp.fromDate(DateTime.now()))
                  .count()
                  .get())
              .count ??
          0;
      aboutToExpireCount = (await FBFireStore.usersDocs
                  .where('vehicleDoc', isEqualTo: true)
                  .where('isArchived', isEqualTo: false)
                  .where('docName', whereIn: [
                    'Vehicle Fitness',
                    'Insurance',
                    'Vehicle Permit',
                  ])
                  .where('fieldData.Expiry_Date.value',
                      isGreaterThanOrEqualTo:
                          Timestamp.fromDate(DateTime.now()))
                  .where('fieldData.Expiry_Date.value',
                      isLessThanOrEqualTo: Timestamp.fromDate(
                          DateTime.now().add(Duration(days: 30))))
                  .count()
                  .get())
              .count ??
          0;
      setState(() {});
    } catch (e) {
      print('Error fetching counts: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PageHeaderWithButton(
                title: 'Expired Docs',
                button: false,
                buttonName: 'New',
                icon: CupertinoIcons.add,
                onPressed: () {
                  // TODO: Implement new policy form
                },
              ),
              const SizedBox(height: 20),
              SearchField(
                searchController: searchController,
                onChanged: (p0) async {
                  setState(() {});
                },
              ),
              const SizedBox(height: 20),
              TabBar(
                physics: NeverScrollableScrollPhysics(),
                controller: _tabController,
                labelColor: primaryColor,
                unselectedLabelColor: Colors.grey[600],
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontSize: 16,
                ),
                indicatorColor: primaryColor,
                indicatorWeight: 2,
                indicatorSize: TabBarIndicatorSize.tab,
                onTap: (index) => _selectTab(index),
                tabs: [
                  Tab(text: 'Expired (${expiredCount ?? 0})'),
                  Tab(text: 'About to Expire (${aboutToExpireCount ?? 0})'),
                ],
              ),
              const SizedBox(height: 20),
              Expanded(
                child: TabBarView(
                  physics: NeverScrollableScrollPhysics(),
                  controller: _tabController,
                  children: [
                    buildDocumentsTab(isExpired: true),
                    buildDocumentsTab(isExpired: false),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildDocumentsTab({required bool isExpired}) {
    Future<QuerySnapshot> getDocumentsStream({
      required bool isExpired,
      DocumentSnapshot? startAfterDoc,
      int pageSize = 10,
    }) async {
      final DateTime today = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
      );
      final DateTime checkdate = today.add(Duration(days: 30));

      Query query = FBFireStore.usersDocs
          .where(Filter.or(
            Filter('docName', isEqualTo: 'Insurance'),
            Filter('docName', isEqualTo: 'Vehicle Fitness'),
            Filter('docName', isEqualTo: 'Vehicle Permit'),
          ))
          .where('vehicleDoc', isEqualTo: true)
          .where('isArchived', isEqualTo: false);

      if (isExpired) {
        query = query.where(
          'fieldData.Expiry_Date.value',
          isLessThan: Timestamp.fromDate(today),
        );
      } else {
        query = query
            .where(
              'fieldData.Expiry_Date.value',
              isGreaterThanOrEqualTo: Timestamp.fromDate(today),
            )
            .where(
              'fieldData.Expiry_Date.value',
              isLessThanOrEqualTo: Timestamp.fromDate(checkdate),
            );
      }

      if (searchController.text.length > 2) {
        query = query
            .where('userName',
                isGreaterThanOrEqualTo:
                    searchController.text.trim().toLowerCase())
            .where('userName',
                isLessThanOrEqualTo:
                    "${searchController.text.trim().toLowerCase()}\uf7ff");
      }

      if (searchController.text.length < 3) {
        query = query
            .orderBy('fieldData.Expiry_Date.value', descending: true)
            .limit(pageSize);
        if (startAfterDoc != null) {
          query = query.startAfterDocument(startAfterDoc);
        }
      }

      return query.get();
    }

    return FutureBuilder(
        // key: ValueKey(DateTime.now()),
        future: getDocumentsStream(
          isExpired: isExpired,
          startAfterDoc: documentStack.isNotEmpty ? documentStack.last : null,
          pageSize: expiryPerPageLimit,
        ),
        builder: (homeController, snapshot) {
          if (snapshot.hasError) {
            print(snapshot.error);
            return Text("Facing some issue!!");
          } else if (snapshot.hasData) {
            return GetBuilder<HomeCtrl>(builder: (homeController) {
              final docs = snapshot.data!.docs;
              if (docs.isNotEmpty) {
                lastDocument = docs.last;
                if (documentStack.isEmpty) {
                  documentStack.add(docs.first);
                }
              }

              List<UserDocsModel> documents =
                  docs.map((e) => UserDocsModel.fromSnapshot(e)).toList();

              List<VehicleModel> vehicles = homeController.vehicles.toList();

              List<Widget> docsWidgets = [];

              for (int index = 0; index < vehicles.length; index++) {
                docsWidgets.clear();
                for (int docIndex = 0;
                    docIndex < documents.length;
                    docIndex++) {
                  final expiryField =
                      documents[docIndex].fieldData?.firstWhereOrNull(
                            (field) =>
                                field.type.toLowerCase() == 'expirydate' &&
                                field.title.toLowerCase() == 'expiry date',
                          );

                  docsWidgets.add(Container(
                    decoration: docIndex % 2 != 0
                        ? BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            // color: Colors.grey[200],
                            color: primaryColor.withValues(alpha: 0.1))
                        : null,
                    child: Row(
                      children: [
                        SizedBox(
                          width: 40,
                          child: UniversalImage(
                            SvgsConstants.document,
                            fit: BoxFit.fitHeight,
                          ),
                        ),
                        SizedBox(
                          width: 80,
                          child: Text(
                            textAlign: TextAlign.center,
                            "${((currentPage - 1) * expiryPerPageLimit) + (docIndex + 1)}",
                            style: TextStyle(
                              fontSize: AppInsets.s14,
                              color: ColorConstants.black,
                              fontWeight: FontWeight.w500,
                              fontFamily: AppFonts.lexend,
                            ),
                            // maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            documents[docIndex].docName.toUpperCase(),
                            style: TextStyle(
                              fontSize: AppInsets.s14,
                              color: ColorConstants.black,
                              fontWeight: FontWeight.w500,
                              fontFamily: AppFonts.lexend,
                            ),
                            // maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 5),
                        Expanded(
                          child: Text(
                            documents[docIndex].vehicleNum ?? "",
                            style: TextStyle(
                              fontSize: AppInsets.s14,
                              color: ColorConstants.black,
                              fontWeight: FontWeight.w500,
                              fontFamily: AppFonts.lexend,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 5),
                        Expanded(
                          child: Text(
                            documents[docIndex].userName,
                            style: TextStyle(
                              fontSize: AppInsets.s14,
                              color: ColorConstants.black,
                              fontWeight: FontWeight.w500,
                              fontFamily: AppFonts.lexend,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 5),
                        Expanded(
                          child: Text(
                            documents[docIndex].userContact,
                            style: TextStyle(
                              fontSize: AppInsets.s14,
                              color: ColorConstants.black,
                              fontWeight: FontWeight.w500,
                              fontFamily: AppFonts.lexend,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 5),
                        Expanded(
                          child: Text(
                            (expiryField?.value.toDate() as DateTime)
                                .goodDayDate(),
                            style: TextStyle(
                              fontSize: AppInsets.s14,
                              color: ColorConstants.black,
                              fontWeight: FontWeight.w500,
                              fontFamily: AppFonts.lexend,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const SizedBox(width: 5),
                        SizedBox(
                          width: 40,
                          child: IconButton(
                            highlightColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            onPressed: () {
                              editDocument(
                                  context,
                                  documents[docIndex],
                                  documents[docIndex].fieldData!,
                                  vehicles[index]);
                            },
                            icon: const Icon(Icons.edit, color: primaryColor),
                          ),
                        ),
                        SizedBox(
                          width: 40,
                          child: IconButton(
                            highlightColor: Colors.transparent,
                            hoverColor: Colors.transparent,
                            onPressed: () {
                              deleteVehicleDocumentById(
                                  vehicleDocId: vehicles[index].docId,
                                  documentId: documents[docIndex].documentId);
                            },
                            icon: const Icon(Icons.delete, color: primaryColor),
                          ),
                        ),
                        (documents[docIndex].files.isEmpty ||
                                documents[docIndex].files.first.fileUrl == '')
                            ? SizedBox(
                                width: 40,
                              )
                            : PopupMenuButton<String>(
                                onSelected: documents[docIndex].files.isEmpty
                                    ? (value) {}
                                    : (value) {
                                        if (value == 'edit') {
                                          editDocument(
                                              context,
                                              documents[docIndex],
                                              documents[docIndex].fieldData!,
                                              vehicles[index]);
                                        } else if (value == 'delete') {
                                          deleteVehicleDocumentById(
                                              vehicleDocId:
                                                  vehicles[index].docId,
                                              documentId: documents[docIndex]
                                                  .documentId);
                                        } else if (value == 'download') {
                                          for (var element
                                              in documents[docIndex].files) {
                                            _launchUrl(element.fileUrl);
                                          }
                                          // _launchUrl(
                                          // paginatedList[docIndex]
                                          //     .fileUrl);
                                        } else if (value == 'view') {
                                          showFilePreviewDialog(context,
                                              documents[docIndex].files);
                                        } else if (value == 'copy') {}
                                      },
                                itemBuilder: (BuildContext context) =>
                                    <PopupMenuEntry<String>>[
                                  if (documents[docIndex].files.isNotEmpty) ...[
                                    const PopupMenuItem<String>(
                                      value: 'download',
                                      child: Text('Download'),
                                    ),
                                    const PopupMenuItem<String>(
                                      value: 'view',
                                      child: Text('View'),
                                    ),
                                  ],
                                ],
                                icon: Icon(
                                  Icons.more_vert,
                                  color: documents[docIndex].files.isEmpty
                                      ? Colors.grey
                                      : primaryColor,
                                ),
                              ),
                      ],
                    ),
                  ));
                }
              }

              return SingleChildScrollView(
                child: Column(
                  // spacing: AppInsets.s16,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: primaryColor,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(
                            width: 40,
                          ),
                          const SizedBox(
                            width: 80,
                            child: Text(
                              'Sr No',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  letterSpacing: 1.2,
                                  color: Colors.white),
                            ),
                          ),
                          const SizedBox(width: 10),
                          const Expanded(
                              child: TableHeaderText(headerName: 'Doc Name')),

                          const SizedBox(width: 5),
                          const Expanded(
                              child: TableHeaderText(
                                  headerName: 'Vehicle Number')),
                          const SizedBox(width: 5),
                          const Expanded(
                              child: TableHeaderText(headerName: 'User Name')),

                          const SizedBox(width: 5),
                          const Expanded(
                              child:
                                  TableHeaderText(headerName: 'Mobile Number')),

                          const SizedBox(width: 5),
                          const Expanded(
                              child:
                                  TableHeaderText(headerName: 'Expiry Date')),

                          // Opacity(
                          //   opacity: 0,
                          //   child: SizedBox(
                          //       width: 60,
                          //       child: Transform.scale(
                          //         scale: .65,
                          //         child: CupertinoSwitch(
                          //           value: true,
                          //           onChanged: (value) {},
                          //         ),
                          //       )),
                          // ),
                          const SizedBox(width: 5),
                          Opacity(
                            opacity: 0,
                            child: SizedBox(
                              width: 60,
                              child: IconButton(
                                highlightColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                onPressed: () {},
                                icon: const Icon(
                                  Icons.delete,
                                  color: primaryColor,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 50,
                          ),
                        ],
                      ),
                    ),
                    ...docsWidgets,
                    SizedBox(
                      height: 20,
                    ),
                    Center(
                        child: PaginationArrowsExpiry(
                            nextsBlocked: !(docs.length == expiryPerPageLimit),
                            previousBlocked: !(documentStack.length > 1),
                            onPrevious:
                                documentStack.length > 1 ? goToPrevious : null,
                            // () {
                            //   goToPrevious();
                            //   setState(() {});
                            // },
                            onNext: docs.length == expiryPerPageLimit
                                ? goToNext
                                : null
                            // () {
                            // goToNext();
                            // setState(() {});
                            // }
                            )),
                  ],
                ),
              );
            });
          } else {
            return Center(child: CircularProgressIndicator());
          }
        });
  }

  _selectTab(int index) {
    currentPage = 1;
    lastDocument = null;
    documentStack.clear();
    setState(() {});
    // setState(() {
    // _selectedTabIndex = index;

    // });
    _tabController.animateTo(index,
        duration: Duration(seconds: 0), curve: Curves.linear);
  }

  void showFilePreviewDialog(BuildContext context, List<DocFiles> files) {
    int currentIndex = 0;
    showDialog(
      context: context,
      builder: (dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(16),
            constraints: const BoxConstraints(
              maxHeight: 600,
              maxWidth: 500,
            ), // Optional: Prevent overflows on small screens
            child:
                //  Column(
                //   mainAxisSize: MainAxisSize.min,
                //   crossAxisAlignment: CrossAxisAlignment.center,
                //   mainAxisAlignment: MainAxisAlignment.center,
                //   children: [
                //     Row(
                //       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                //       children: [
                //         Text(
                //           'Preview',
                //           style: Theme.of(context).textTheme.titleLarge,
                //         ),
                //         IconButton(
                //             onPressed: () {
                //               Clipboard.setData(ClipboardData(
                //                       text: files[currentIndex].fileUrl))
                //                   .then((_) {
                //                 ScaffoldMessenger.of(context).showSnackBar(
                //                   SnackBar(
                //                       content: Text('Link copied to clipboard!')),
                //                 );
                //               });
                //             },
                //             icon: Icon(Icons.copy))
                //       ],
                //     ),
                //     const SizedBox(height: 16),
                //     CarouselSlider(
                //       options: CarouselOptions(
                //         enableInfiniteScroll: false,
                //         aspectRatio: 1.4,
                //         onPageChanged: (index, reason) {
                //           currentIndex = index;
                //         },
                //       ),
                //       items: [
                //         ...List.generate(files.length, (index) {
                //           return Flexible(
                //             child: ClipRRect(
                //               borderRadius: BorderRadius.circular(8),
                //               child: files[index].fileUrl == ''
                //                   ? UniversalImage(SvgsConstants.document)
                //                   : files[index].fileType == 'image'
                //                       ? UniversalImage(
                //                           files[index].fileUrl,
                //                           fit: BoxFit.contain,
                //                         )
                //                       : SfPdfViewer.network(files[index].fileUrl),
                //             ),
                //           );
                //         }),
                //         const SizedBox(height: 12),
                //         Align(
                //           alignment: Alignment.centerRight,
                //           child: TextButton(
                //             onPressed: () => Navigator.of(dialogContext).pop(),
                //             child: const Text("Close"),
                //           ),
                //         )
                //       ],
                //     ),
                //   ],
                // ),
                Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Preview',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    Spacer(),
                    IconButton(
                        onPressed: () {
                          Clipboard.setData(ClipboardData(
                                  text: files[currentIndex].fileUrl))
                              .then((_) {
                            // Show a confirmation message after copying
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text('Link copied to clipboard!')),
                            );
                          });
                        },
                        icon: Icon(Icons.copy)),
                    IconButton(
                        onPressed: () {
                          launchUrl(Uri.parse(files[currentIndex].fileUrl));
                        },
                        icon: Icon(Icons.download))
                  ],
                ),
                const SizedBox(height: 16),
                CarouselSlider(
                  options: CarouselOptions(
                    enableInfiniteScroll: false,
                    aspectRatio: 1,
                    onPageChanged: (index, reason) {
                      currentIndex = index;
                    },
                  ),
                  items: [
                    ...List.generate(
                      files.length,
                      (index) {
                        print(files[index].fileType == 'image');
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: files[index].fileType == 'image'
                              ? UniversalImage(
                                  files[index].fileUrl,
                                  fit: BoxFit.contain,
                                )
                              : true
                                  ? InkWell(
                                      hoverColor: Colors.transparent,
                                      splashColor: Colors.transparent,
                                      onTap: () async {
                                        final pdfUrl = files[index].fileUrl;
                                        final googleDocsUrl =
                                            'https://docs.google.com/gview?embedded=true&url=${Uri.encodeComponent(pdfUrl)}';

                                        final uri = Uri.parse(googleDocsUrl);

                                        if (await canLaunchUrl(uri)) {
                                          await launchUrl(uri,
                                              mode: LaunchMode
                                                  .externalApplication);
                                        } else {
                                          throw 'Could not launch PDF viewer';
                                        }
                                        context.pop();
                                      },
                                      child: SizedBox(
                                          height: 25,
                                          width: 25,
                                          child: Image.asset(
                                              'assets/images/share.png')),
                                    )
                                  : SfPdfViewer.network(
                                      onDocumentLoaded: (details) {
                                        setState(() {});
                                      },
                                      files[index].fileUrl,
                                    ),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () => Navigator.of(dialogContext).pop(),
                    child: const Text("Close"),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> editDocument(BuildContext context, UserDocsModel task,
      List<TaskDetailModel> taskDetails, VehicleModel vehicleModel) async {
    try {
      List<SelectedFile?> selectedFile = [];
      bool loading = false;
      final Map<String, TextEditingController> controllers = {};

      // Initialize controllers with values from the task details
      for (var detail in taskDetails) {
        controllers.addAll({detail.title: TextEditingController(text: '')});

        controllers[detail.title] = (detail.value is Timestamp)
            ? TextEditingController(
                text: (detail.value as Timestamp).toDate().convertToYYYYMMDD())
            : TextEditingController(text: detail.value);
      }

      showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState2) {
              return AlertDialog(
                title: Text('Add Documents for ${task.docName}'),
                content: SizedBox(
                  width: 280,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ...taskDetails.map((taskDetail) {
                        final controller = controllers[taskDetail.title]!;
                        print(controller.text);

                        switch (taskDetail.type.toLowerCase()) {
                          case 'textfield':
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: TextFormField(
                                controller: controller,
                                onChanged: (newValue) {
                                  taskDetail.value = newValue;
                                },
                                cursorHeight: 20,
                                decoration: inpDecor().copyWith(
                                  labelText:
                                      'Enter value for ${taskDetail.title}',
                                ),
                              ),
                            );
                          case 'datepicker':
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              child: TextFormField(
                                controller: controller,
                                decoration: inpDecor().copyWith(
                                  labelText:
                                      'Select a date for ${taskDetail.title}',
                                ),
                                onTap: () async {
                                  DateTime? selectedDate = await showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now(),
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime(2101),
                                  );
                                  if (selectedDate != null) {
                                    final formatted =
                                        "${selectedDate.toLocal()}"
                                            .split(' ')[0];
                                    controller.text = formatted;
                                    taskDetail.value = formatted;
                                  }
                                },
                                readOnly: true,
                              ),
                            );
                          case 'expirydate':
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                DateFormatField(
                                    controller: controller,
                                    decoration: inpDecor().copyWith(
                                      labelText:
                                          'Enter a date for ${(taskDetail.title)}',
                                      // errorText: errorText,
                                    ),
                                    type: DateFormatType.type1,
                                    initialDate: (taskDetail.value as Timestamp)
                                        .toDate(),
                                    onComplete: (date) {
                                      print(date.toString);
                                      if (date != null) {
                                        final formatted =
                                            "${date.toLocal()}".split(' ')[0];
                                        controller.text = formatted;
                                        taskDetail.value = date;
                                        setState2(() {
                                          // errors[taskDetail.title] = null;
                                        });
                                      }
                                    }),
                                const SizedBox(height: 10),
                              ],
                            );
                          default:
                            return const SizedBox.shrink();
                        }
                      }),
                      const SizedBox(height: 16),
                      InkWell(
                        highlightColor: Colors.transparent,
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        hoverColor: Colors.transparent,
                        onTap: () async {
                          try {
                            final file = await ImagePickerService()
                                .pickFile(context, true);
                            if (file.isNotEmpty) {
                              selectedFile.addAll(file);
                            }
                            setState2(() {});
                          } catch (e) {
                            debugPrint("Failed to pick doc: $e");
                            setState2(() {
                              loading = false;
                            });
                          }
                        },
                        child: Container(
                            padding: EdgeInsets.all(10),
                            width: double.infinity,
                            height: 150,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(7),
                              color: Colors.transparent,
                            ),
                            child: selectedFile.isEmpty
                                ? const Icon(Icons.file_copy_outlined)
                                : Wrap(
                                    spacing: 10,
                                    runSpacing: 10,
                                    children: [
                                      ...List.generate(
                                        selectedFile.length,
                                        (index) {
                                          return selectedFile[index] == null
                                              ? SizedBox()
                                              : selectedFile[index]?.type ==
                                                      'image'
                                                  ? SizedBox(
                                                      height: 50,
                                                      width: 50,
                                                      child: Image.memory(
                                                        selectedFile[index]!
                                                            .uInt8List,
                                                        width: double.infinity,
                                                        fit: BoxFit.cover,
                                                      ),
                                                    )
                                                  : Column(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        const Icon(Icons
                                                            .picture_as_pdf),
                                                        Text(selectedFile[index]
                                                                ?.name ??
                                                            ""),
                                                      ],
                                                    );
                                        },
                                      )
                                    ],
                                  )),
                      ),
                    ],
                  ),
                ),
                actions: loading
                    ? const [
                        Center(
                          child: SizedBox(
                            height: 25,
                            width: 25,
                            child: CircularProgressIndicator(strokeWidth: 2.5),
                          ),
                        )
                      ]
                    : [
                        TextButton(
                          onPressed: () async {
                            try {
                              setState2(() {
                                loading = true;
                              });

                              // final fileUrl = selectedFile != null
                              //     ? await uploadUserDocs(selectedFile!)
                              //     : "";
                              //
                              // final uuid = Uuid();
                              // final documentId = uuid.v4();
                              // final Map<String, dynamic> newTaskDoc = {
                              //   'documentId': documentId,
                              //   'fileType': selectedFile?.type ?? '',
                              //   'fileUrl': fileUrl,
                              //   'fieldData':
                              //       taskDetails.map((e) => e.toJson()).toList(),
                              // };

                              final userDocRef =
                                  FBFireStore.usersDocs.doc(task.documentId);

// 1. Get the user's existing document
                              final userDoc = await userDocRef.get();
                              // List<dynamic> existingDocs =
                              //     userDoc.data()?['vehicleDocuments'] ?? [];

// 2. Check if a document for this task already exists
                              // final docIndex = existingDocs.indexWhere(
                              //     (doc) => doc['docName'] == taskName);
                              Map fieldData = {};
                              for (var detail in taskDetails) {
                                fieldData.addEntries(detail.toJson().entries);
                              }
                              List<Map> files = [];
                              for (var element in selectedFile) {
                                final fileUrl = element != null
                                    ? await uploadUserDocs(element)
                                    : "";
                                files.add({element?.type: fileUrl});
                              }
                              // final newFieldData =
                              //     taskDetails.map((e) => e.toJson()).toList();

                              // if (docIndex >= 0) {
                              // 3. Update the specific document's fields
                              final existingDoc = userDoc.data() ?? {};
                              existingDoc['fieldData'] = fieldData;
                              existingDoc['files'] = files;
                              // existingDoc['fileUrl'] = selectedFile != null
                              //     ? await uploadUserDocs(selectedFile!)
                              //     : '';

                              // existingDocs[docIndex] = existingDoc;
                              // } else {
                              //   final uuid = Uuid();
                              //   final documentId = uuid.v4();
                              //   final Map<String, dynamic> newTaskDoc = {
                              //     'documentId': documentId,
                              //     'docName': taskName,
                              //     'fileType': selectedFile?.type ?? '',
                              //     'fileUrl': selectedFile != null
                              //         ? await uploadUserDocs(selectedFile!)
                              //         : '',
                              //     "isDeleted": false,
                              //     'fieldData': newFieldData,
                              //   };
                              //   existingDocs.add(newTaskDoc);
                              // }

// 5. Update the entire `documents` array
                              userDocRef.update(existingDoc);
                              if (context.mounted) {
                                Navigator.of(context).pop();
                                setState(() {});
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text("Document updated"),
                                  ),
                                );
                              }
                            } catch (e) {
                              debugPrint("Error saving document: $e");
                            } finally {
                              setState2(() {
                                loading = false;
                              });
                            }
                          },
                          child: const Text("Add"),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text("Cancel"),
                        ),
                      ],
              );
            },
          );
        },
      );
    } catch (e) {
      debugPrint("Error loading task details: $e");
    }
  }

  Future<void> deleteVehicleDocumentById({
    required String vehicleDocId,
    required String documentId,
  }) async {
    debugPrint("Document ID to delete: $documentId");

    try {
      // Fetch the vehicle document
      // final vehicleDocSnapshot =
      //     await FBFireStore.vehicles.doc(vehicleDocId).get();
      await FBFireStore.usersDocs.doc(documentId).delete();

      // if (vehicleDocSnapshot.exists) {
      //   final vehicleDoc = vehicleDocSnapshot.data();
      //   final List<dynamic> documents = vehicleDoc?['vehicleDocuments'] ?? [];

      //   // Find the document with the matching documentId
      //   final documentToDelete = documents.firstWhere(
      //     (doc) => doc['documentId'] == documentId,
      //     orElse: () => null,
      //   );

      //   if (documentToDelete != null) {
      //     // Remove the exact matching document
      //     await FBFireStore.vehicles.doc(vehicleDocId).update({
      //       'vehicleDocuments': FieldValue.arrayRemove([documentToDelete]),
      //     });

      //     debugPrint("Document deleted successfully.");
      //   } else {
      //     debugPrint("Document with ID $documentId not found.");
      //   }
      // } else {
      //   debugPrint("Vehicle document not found.");
      // }
    } catch (error) {
      debugPrint("Error deleting vehicle document: $error");
    }
  }

  Future<void> _launchUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.inAppWebView,
        webOnlyWindowName: '_blank',
      );
    } else {
      throw 'Could not launch $url';
    }
  }

  Future<UserModel?> getUser(String userId) async {
    try {
      final query = await FBFireStore.users
          .where('userid', isEqualTo: userId)
          .limit(1)
          .get();
      if (query.docs.isNotEmpty) {
        final user = UserModel.fromDocSnap(query.docs.first);
        return user;
      } else {
        return null; // user not found
      }
    } catch (e) {
      print('Error fetching user name: $e');
      return null;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}

class PaginationArrowsExpiry extends StatelessWidget {
  final bool previousBlocked;
  final bool nextsBlocked;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;

  const PaginationArrowsExpiry({
    super.key,
    required this.nextsBlocked,
    required this.previousBlocked,
    this.onPrevious,
    this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    Widget arrowButton({
      required IconData icon,
      required VoidCallback? onTap,
      bool disabled = false,
    }) {
      return GestureDetector(
        onTap: disabled ? null : onTap,
        child: Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: disabled ? Colors.grey.shade300 : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              if (!disabled)
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
            ],
            border: Border.all(
              color: disabled ? Colors.grey.shade400 : Colors.black12,
            ),
          ),
          child: Icon(
            icon,
            color: disabled ? Colors.grey : Colors.black,
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        arrowButton(
          icon: Icons.chevron_left,
          onTap: onPrevious,
          disabled: previousBlocked,
        ),
        SizedBox(
          width: 20,
        ),
        SizedBox(
          width: 20,
        ),
        arrowButton(
          icon: Icons.chevron_right,
          onTap: onNext,
          disabled: nextsBlocked,
        ),
      ],
    );
  }
}
