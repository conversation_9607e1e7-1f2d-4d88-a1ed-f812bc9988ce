import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:html_editor_enhanced/html_editor.dart';

import '../../../shared/constants/colors.dart';
import '../../../shared/firebase.dart';
import '../../controller/home_controller.dart';
import '../../models/news_model.dart';
import '../../shared/router.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';

class NewsPage extends StatefulWidget {
  const NewsPage({super.key});

  @override
  State<NewsPage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> {
  final HtmlEditorController _htmlEditorController = HtmlEditorController();
  final TextEditingController _titleController = TextEditingController();
  bool loading = false;

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(
            title: 'News',
            button: true,
            buttonName: 'Add News',
            icon: CupertinoIcons.add,
            onPressed: () async {
              context.push('${WebRoutes.addNews}/${'new'}');
            },
          ),
          const SizedBox(height: 20),
          GetBuilder<HomeCtrl>(builder: (homeController) {
            return _myDocumentList(homeController.newsList);
          })
        ],
      ),
    );
  }

  _myDocumentList(List<NewsModel> newsList) {
    return ListView.separated(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(vertical: AppInsets.s16),
      itemCount: newsList.length,
      itemBuilder: (context, index) {
        NewsModel newsModel = newsList[index];

        return InkWell(
          highlightColor: Colors.transparent,
          overlayColor: const WidgetStatePropertyAll(Colors.transparent),
          hoverColor: Colors.transparent,
          onTap: () => context.push(
              '${WebRoutes.newDetails}/${newsModel.docId}?title=${Uri.encodeComponent(newsModel.title)}&desc=${Uri.encodeComponent(newsModel.description)}'),
          child: Padding(
            padding: const EdgeInsets.only(right: AppInsets.s12),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: AppInsets.s12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(AppInsets.s4)),
                border: Border.all(
                  color: ColorConstants.borderColor,
                  width: AppInsets.s1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: AppInsets.s8),
                    child: Container(
                      padding: const EdgeInsets.all(AppInsets.s6),
                      width: AppInsets.s48,
                      height: AppInsets.s48,
                      decoration: BoxDecoration(
                        color: ColorConstants.grayBackGroundColor,
                        borderRadius: BorderRadius.circular(AppInsets.s4),
                      ),
                      child: Icon(
                        Icons.newspaper,
                        color: primaryColor,
                        size: AppInsets.s24,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppInsets.s16),
                  Expanded(
                    child: Text(
                      newsModel.title.toUpperCase(),
                      style: TextStyle(
                        fontSize: AppInsets.s16,
                        color: ColorConstants.black,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppFonts.lexend,
                      ),
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: AppInsets.s16),
                  InkWell(
                    highlightColor: Colors.transparent,
                    overlayColor:
                        const WidgetStatePropertyAll(Colors.transparent),
                    hoverColor: Colors.transparent,
                    onTap: () => context.push(
                        '${WebRoutes.addNews}/${newsModel.docId}?title=${Uri.encodeComponent(newsModel.title)}&desc=${Uri.encodeComponent(newsModel.description)}'),
                    child: Icon(
                      Icons.edit,
                      color: primaryColor,
                      size: AppInsets.s24,
                    ),
                  ),
                  const SizedBox(width: AppInsets.s16),
                  InkWell(
                    highlightColor: Colors.transparent,
                    overlayColor:
                        const WidgetStatePropertyAll(Colors.transparent),
                    hoverColor: Colors.transparent,
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) {
                          bool loading = false;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return AlertDialog(
                                title: const Text("Alert"),
                                content: const Text(
                                    "Are you sure you want to delete"),
                                actions: loading
                                    ? [
                                        const Center(
                                          child: SizedBox(
                                            height: 25,
                                            width: 25,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2.5,
                                            ),
                                          ),
                                        )
                                      ]
                                    : [
                                        TextButton(
                                            onPressed: () async {
                                              try {
                                                try {
                                                  deleteVehicleDocumentById(
                                                      documentId:
                                                          newsModel.docId);
                                                } on Exception catch (e) {
                                                  debugPrint(e.toString());
                                                }

                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                }
                                              } catch (e) {
                                                debugPrint(e.toString());

                                                if (context.mounted) {
                                                  Navigator.of(context).pop();
                                                }
                                              }
                                            },
                                            child: const Text('Yes')),
                                        TextButton(
                                            onPressed: () {
                                              Navigator.of(context).pop();
                                            },
                                            child: const Text('No')),
                                      ],
                              );
                            },
                          );
                        },
                      );
                    },
                    child: Icon(
                      Icons.delete,
                      color: primaryColor,
                      size: AppInsets.s24,
                    ),
                  ),
                  const SizedBox(width: AppInsets.s16),
                  if (newsModel.description.isNotEmpty) ...[
                    Icon(
                      Icons.keyboard_arrow_right,
                      color: primaryColor,
                      size: AppInsets.s24,
                    ),
                  ],
                  const SizedBox(width: AppInsets.s8),
                ],
              ),
            ),
          ),
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return const SizedBox(height: AppInsets.s8);
      },
    );
  }

  Future<void> deleteVehicleDocumentById({
    required String documentId,
  }) async {
    debugPrint("Document ID to delete: $documentId");

    try {
      // Fetch the vehicle document
      await FBFireStore.news.doc(documentId).delete().then((v) {
        const snackBar = SnackBar(
          content: Text("Deleted"),
          duration: Duration(seconds: 2),
        );
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
        }
      });
    } catch (error) {
      debugPrint("Error deleting vehicle document: $error");
    }
  }
}
