import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:mass_ibs/shared/constants/constants.dart';

import '../common/page_header.dart';

class NewsDetailsPage extends StatefulWidget {
  const NewsDetailsPage({
    super.key,
    required this.id,
    required this.title,
    required this.description,
  });

  final String id;
  final String title;
  final String description;

  @override
  State<NewsDetailsPage> createState() => _NewsDetailsPageState();
}

class _NewsDetailsPageState extends State<NewsDetailsPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(
            title: widget.title,
            button: false,
            back: true,
            buttonName: 'Add News',
            // icon: CupertinoIcons.add,
            onPressed: () async {},
          ),
          const SizedBox(height: 20),
          Divider(
            color: ColorConstants.grayBackGroundColor,
          ),
          Html(
            shrinkWrap: true,
            data: widget.description,
          ),
        ],
      ),
    );
  }
}
