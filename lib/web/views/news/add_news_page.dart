import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:html_editor_enhanced/html_editor.dart';
import 'package:mass_ibs/shared/widgets/modal_progress_hud.dart';

import '../../../shared/firebase.dart';
import '../../shared/methods.dart';
import '../common/page_header.dart';

class AddNewsPage extends StatefulWidget {
  const AddNewsPage({
    super.key,
    required this.id,
    required this.title,
    required this.description,
  });

  final String id;
  final String title;
  final String description;

  @override
  State<AddNewsPage> createState() => _AddNewsPageState();
}

class _AddNewsPageState extends State<AddNewsPage> {
  late HtmlEditorController _htmlEditorController;
  late TextEditingController _titleController;
  bool loading = false;
  bool isUpdate = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    _htmlEditorController = HtmlEditorController(processInputHtml: true);
    _titleController = TextEditingController();

    if (widget.title.isNotEmpty || widget.description.isNotEmpty) {
      setState(() {
        isUpdate = true;
      });
    }
    loadData();
  }

  void loadData() async {
    await Future.delayed(Duration(milliseconds: 500));
    setState(() {
      _titleController.text = widget.title;
      _htmlEditorController.insertHtml(widget.description);
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    return ModalProgressHUD(
      inAsyncCall: loading,
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PageHeaderWithButton(
              title: 'Add News',
              button: true,
              buttonName: 'Save',
              back: true,
              icon: CupertinoIcons.add,
              onPressed: () async {
                final isValid = _formKey.currentState?.validate() ?? false;
                final description = await _htmlEditorController.getText();

                if (!isValid) return;

                if (description.trim().isEmpty ||
                    description.trim() == "<p></p>") {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                        content: Text('Description cannot be empty')),
                  );
                  return;
                }

                try {
                  setState(() {
                    loading = true;
                  });

                  final description = await _htmlEditorController.getText();

                  final data = {
                    'title': _titleController.text.trim(),
                    'description': description,
                  };

                  if (isUpdate && widget.id.isNotEmpty) {
                    // ✅ Update existing document
                    await FBFireStore.news.doc(widget.id).update(data);
                    final notiData = {
                      'desc': _titleController.text.trim(),
                      'title': 'News update!!',
                      'test': false,
                      'createdAt': FieldValue.serverTimestamp(),
                    };
                    try {
                      await FBFireStore.notifications.add(notiData);
                    } catch (e, stack) {
                      debugPrint(
                          "🔥 Critical Firestore Error: ${e.toString()}");
                      debugPrint("Stack trace: $stack");
                      rethrow;
                    }
                  } else {
                    // ✅ Add new document
                    await FBFireStore.news.add(data);
                    final notiData = {
                      'desc': _titleController.text.trim(),
                      'title': 'News update!!',
                      'test': false,
                      'createdAt': FieldValue.serverTimestamp(),
                    };
                    try {
                      await FBFireStore.notifications.add(notiData);
                    } catch (e, stack) {
                      debugPrint(
                          "🔥 Critical Firestore Error: ${e.toString()}");
                      debugPrint("Stack trace: $stack");
                      rethrow;
                    }
                  }

                  _titleController.clear();
                  _htmlEditorController.clear();

                  setState(() {
                    loading = false;
                  });

                  var snackBar = SnackBar(
                    content: Text(
                        isUpdate ? "News Updated" : "News Saved Successfully"),
                    duration: const Duration(seconds: 2),
                  );

                  Navigator.pop(context);
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(snackBar);
                  }
                } catch (e) {
                  setState(() {
                    loading = false;
                  });
                  debugPrint('Error: $e');
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text("Failed to save news"),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  }
                }
              },
            ),
            const SizedBox(height: 20),
            Center(
              child: Container(
                height: screenHeight,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      spreadRadius: 2,
                    )
                  ],
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _titleController,
                        cursorHeight: 20,
                        decoration: inpDecor().copyWith(labelText: 'Title'),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Title cannot be empty';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),
                      HtmlEditor(
                        controller: _htmlEditorController,
                        htmlToolbarOptions: HtmlToolbarOptions(
                          toolbarPosition: ToolbarPosition.aboveEditor,
                          toolbarType: ToolbarType.nativeScrollable,
                        ),
                        otherOptions: OtherOptions(
                            height: MediaQuery.sizeOf(context).height - 200),
                        htmlEditorOptions: HtmlEditorOptions(
                          hint: "Start Typing...",
                          autoAdjustHeight: true,
                          // shouldEnsureVisible: true,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
