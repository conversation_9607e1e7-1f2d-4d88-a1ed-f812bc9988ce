import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/web/models/recent_model.dart';

import '../../../shared/constants/colors.dart';
import '../../controller/home_controller.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';

class RecentsPage extends StatefulWidget {
  const RecentsPage({super.key});

  @override
  State<RecentsPage> createState() => _RecentsPageState();
}

class _RecentsPageState extends State<RecentsPage> {
  bool loading = false;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(
            title: 'Recent Activity',
            button: false,
            buttonName: 'Add',
            icon: CupertinoIcons.add,
            onPressed: () async {},
          ),
          const SizedBox(height: 20),
          GetBuilder<HomeCtrl>(builder: (homeController) {
            return _myDocumentList(homeController.recentList);
          })
        ],
      ),
    );
  }

  _myDocumentList(List<RecentModel> recents) {
    return ListView.separated(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(vertical: AppInsets.s16),
      itemCount: recents.length,
      itemBuilder: (context, index) {
        RecentModel recentModel = recents[index];

        return InkWell(
          highlightColor: Colors.transparent,
          overlayColor: const WidgetStatePropertyAll(Colors.transparent),
          hoverColor: Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.only(right: AppInsets.s12),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: AppInsets.s12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(AppInsets.s4)),
                border: Border.all(
                  color: ColorConstants.borderColor,
                  width: AppInsets.s1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: AppInsets.s8),
                    child: Container(
                      padding: const EdgeInsets.all(AppInsets.s6),
                      width: AppInsets.s48,
                      height: AppInsets.s48,
                      decoration: BoxDecoration(
                        color: ColorConstants.grayBackGroundColor,
                        borderRadius: BorderRadius.circular(AppInsets.s4),
                      ),
                      child: Icon(
                        Icons.timelapse,
                        color: primaryColor,
                        size: AppInsets.s24,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppInsets.s16),
                  Expanded(
                    child: Text(
                      recentModel.message,
                      style: TextStyle(
                        fontSize: AppInsets.s16,
                        color: ColorConstants.black,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppFonts.lexend,
                      ),
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return const SizedBox(height: AppInsets.s8);
      },
    );
  }
}
