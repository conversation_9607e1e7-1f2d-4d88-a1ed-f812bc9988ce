import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/web/models/banner_model.dart';

import '../../../shared/firebase.dart';
import '../../controller/home_controller.dart';
import '../../services/image_picker.dart';
import '../../shared/methods.dart';
import '../common/header_search_feild.dart';
import '../common/page_header.dart';
import 'banner_card.dart';

class AddBannerPage extends StatefulWidget {
  const AddBannerPage({super.key});

  @override
  State<AddBannerPage> createState() => _AddBannerPageState();
}

class _AddBannerPageState extends State<AddBannerPage> {
  final searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          PageHeaderWithButton(
            title: "Add Banner",
            onPressed: () {
              _addBannerForm(context);
            },
            buttonName: 'New',
            button: true,
            icon: CupertinoIcons.add,
          ),
          const SizedBox(height: 20),
          SearchField(
            searchController: searchController,
            onChanged: (p0) {
              setState(() {});
            },
          ),
          const SizedBox(height: 20),
          GetBuilder<HomeCtrl>(builder: (hctrl) {
            final bannersList = hctrl.banners;

            return StaggeredGrid.extent(
              maxCrossAxisExtent: 220,
              crossAxisSpacing: 20,
              mainAxisSpacing: 20,
              children: [
                ...List.generate(
                  bannersList.length,
                  (index) => Stack(
                    children: [
                      BannerCard(
                        bannerId: bannersList[index].docId,
                        imageString: bannersList[index].image,
                        title: bannersList[index].title,
                      ),
                      Align(
                        alignment: Alignment.topRight,
                        child: Tooltip(
                          message: 'Delete',
                          child: Padding(
                            padding: const EdgeInsets.only(right: 8.0, top: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              mainAxisSize: MainAxisSize.min,
                              spacing: 8,
                              children: [
                                InkWell(
                                  onTap: () {
                                    showEditBannerDialog(
                                      context,
                                      documentId: bannersList[index].docId,
                                      initialTitle: bannersList[index].title,
                                      initialLink: bannersList[index].link,
                                      initialImageUrl: bannersList[index].image,
                                    );
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(3),
                                    decoration: const BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                              color: Colors.black26,
                                              blurRadius: 2,
                                              // spreadRadius: 0,
                                              spreadRadius: 1,
                                              offset: Offset(0, 2)),
                                        ]),
                                    child: const Icon(
                                      Icons.edit,
                                      size: 18,
                                    ),
                                  ),
                                ),
                                InkWell(
                                  onTap: () {
                                    showDialog(
                                      context: context,
                                      builder: (context) {
                                        return AlertDialog(
                                          title: const Text('Delete'),
                                          content: const Text(
                                              "Are you sure you want to delete?"),
                                          actions: [
                                            TextButton(
                                                onPressed: () async {
                                                  Navigator.of(context).pop();
                                                  await _deleteImageAndDocument(
                                                      bannersList[index]);
                                                },
                                                child: const Text("Yes")),
                                            TextButton(
                                                onPressed: () async {
                                                  Navigator.of(context).pop();
                                                },
                                                child: const Text("No")),
                                          ],
                                        );
                                      },
                                    );
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(3),
                                    decoration: const BoxDecoration(
                                        color: Colors.white,
                                        shape: BoxShape.circle,
                                        boxShadow: [
                                          BoxShadow(
                                              color: Colors.black26,
                                              blurRadius: 2,
                                              // spreadRadius: 0,
                                              spreadRadius: 1,
                                              offset: Offset(0, 2)),
                                        ]),
                                    child: const Icon(
                                      CupertinoIcons.xmark,
                                      size: 18,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              ],
            );
          })
        ],
      ),
    );
  }

  Future<dynamic> _addBannerForm(BuildContext context) {
    return showDialog(
      context: context,
      builder: (context) {
        bool loading = false;
        SelectedFile? catPickedImage;
        final GlobalKey<FormState> formKey = GlobalKey<FormState>();
        TextEditingController titleCtrl = TextEditingController();
        TextEditingController linkCtrl = TextEditingController();

        return StatefulBuilder(
          builder: (context, setState2) {
            return AlertDialog(
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
              title: const Text("Add Banner"),
              content: SizedBox(
                width: 280,
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextFormField(
                        controller: titleCtrl,
                        cursorHeight: 20,
                        decoration: inpDecor().copyWith(labelText: 'Title'),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Title is required';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 20),
                      TextFormField(
                        controller: linkCtrl,
                        cursorHeight: 20,
                        decoration: inpDecor().copyWith(labelText: 'Link'),
                      ),
                      const SizedBox(height: 20),
                      InkWell(
                        onTap: () async {
                          final res = await ImagePickerService()
                              .pickImageNew(context, useCompressor: true);
                          if (res != null) {
                            catPickedImage = res;
                            setState2(() {});
                          }
                        },
                        child: Container(
                          width: double.maxFinite,
                          height: 150,
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(7),
                            color: Colors.transparent,
                          ),
                          child: catPickedImage == null
                              ? const Icon(
                                  CupertinoIcons.photo_fill_on_rectangle_fill)
                              : Image.memory(
                                  catPickedImage!.uInt8List,
                                  width: double.maxFinite,
                                  fit: BoxFit.cover,
                                ),
                        ),
                      ),
                      if (catPickedImage == null)
                        Align(
                          alignment: Alignment.centerLeft,
                          child: const Padding(
                            padding: EdgeInsets.only(top: 8.0),
                            child: Text(
                              'Banner is required',
                              style: TextStyle(color: Colors.red, fontSize: 12),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
              actions: loading
                  ? [
                      const Center(
                        child: SizedBox(
                          height: 25,
                          width: 25,
                          child: CircularProgressIndicator(strokeWidth: 2.5),
                        ),
                      )
                    ]
                  : [
                      TextButton(
                        onPressed: () async {
                          if (!(formKey.currentState?.validate() ?? false)) {
                            return;
                          }

                          if (catPickedImage == null) {
                            // ScaffoldMessenger.of(context).showSnackBar(
                            //   const SnackBar(content: Text('Please select a banner image')),
                            // );
                            return;
                          }

                          try {
                            setState2(() {
                              loading = true;
                            });

                            final imageUrl =
                                await uploadBannerFile(catPickedImage!);

                            final data = {
                              'title': titleCtrl.text.trim(),
                              'image': imageUrl,
                              'link': linkCtrl.text.trim(),
                            };

                            await FBFireStore.banners.add(data);

                            setState2(() {
                              loading = false;
                            });

                            if (context.mounted) {
                              Navigator.of(context).pop();
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text("Banner Added")),
                              );
                            }
                          } catch (e) {
                            debugPrint(e.toString());
                            setState2(() => loading = false);
                          }
                        },
                        child: const Text("Add"),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text("Cancel"),
                      ),
                    ],
            );
          },
        );
      },
    );
  }

  Future<void> showEditBannerDialog(
    BuildContext context, {
    required String documentId,
    required String initialTitle,
    required String initialLink,
    required String initialImageUrl,
  }) {
    return showDialog(
      context: context,
      builder: (context) {
        final GlobalKey<FormState> formKey = GlobalKey<FormState>();
        TextEditingController titleCtrl =
            TextEditingController(text: initialTitle);
        TextEditingController linkCtrl =
            TextEditingController(text: initialLink);
        SelectedFile? newImage;
        bool loading = false;

        return StatefulBuilder(
          builder: (context, setState2) {
            return AlertDialog(
              title: const Text('Edit Banner'),
              content: SizedBox(
                width: 300,
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      TextFormField(
                        controller: titleCtrl,
                        decoration: inpDecor().copyWith(labelText: 'Title'),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Title is required';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: linkCtrl,
                        decoration: inpDecor().copyWith(labelText: 'Link'),
                      ),
                      const SizedBox(height: 16),
                      InkWell(
                        onTap: () async {
                          final picked = await ImagePickerService()
                              .pickImageNew(context, useCompressor: true);
                          if (picked != null) {
                            newImage = picked;
                            setState2(() {});
                          }
                        },
                        child: Container(
                          height: 150,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: newImage != null
                              ? Image.memory(newImage!.uInt8List,
                                  fit: BoxFit.cover)
                              : Image.network(initialImageUrl,
                                  fit: BoxFit.cover),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              actions: loading
                  ? const [Center(child: CircularProgressIndicator())]
                  : [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () async {
                          if (!(formKey.currentState?.validate() ?? false)) {
                            return;
                          }

                          setState2(() => loading = true);

                          try {
                            String? imageUrl = initialImageUrl;

                            if (newImage != null) {
                              imageUrl = await uploadBannerFile(newImage!);
                            }

                            final data = {
                              'title': titleCtrl.text.trim(),
                              'link': linkCtrl.text.trim(),
                              'image': imageUrl,
                            };

                            await FBFireStore.banners
                                .doc(documentId)
                                .update(data);

                            if (context.mounted) {
                              Navigator.of(context).pop();
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content:
                                        Text('Banner updated successfully')),
                              );
                            }
                          } catch (e) {
                            debugPrint('Edit error: $e');
                            setState2(() => loading = false);
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                    content: Text('Failed to update banner')),
                              );
                            }
                          }
                        },
                        child: const Text('Update'),
                      ),
                    ],
            );
          },
        );
      },
    );
  }

  _deleteImageAndDocument(BannerModel bannerModel) async {
    try {
      // 1. Delete the image from Firebase Storage
      final storageReference = FBStorage.fbStore.refFromURL(bannerModel.image);
      await storageReference.delete(); //

      await FBFireStore.banners.doc(bannerModel.docId).delete();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("Banner Deleted Successfully")));
      }
    } on Exception catch (e) {
      debugPrint(e.toString());
      if (context.mounted) {
        ScaffoldMessenger.of(context)
            .showSnackBar(const SnackBar(content: Text("Facing Some Issue")));
      }
    }
  }
}
