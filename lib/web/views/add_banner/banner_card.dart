import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../shared/methods.dart';

class BannerCard extends StatelessWidget {
  const BannerCard({
    super.key,
    required this.imageString,
    required this.title,
    required this.bannerId,
  });

  final String imageString;
  final String title;
  final String bannerId;

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: 1,
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
          fit: StackFit.expand,
          children: [
            CachedNetworkImage(
              imageUrl: imageString,
              fit: BoxFit.cover,
              placeholder: (context, url) {
                return const Icon(CupertinoIcons.camera);
              },
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                alignment: Alignment.bottomCenter,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    stops: [
                      0.07,
                      0.12,
                      0.15,
                      // 0.2,
                      0.3,
                    ],
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Color.fromARGB(210, 41, 40, 40),
                      Colors.black45,
                      Color.fromARGB(79, 0, 0, 0),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Align(
                  alignment: Alignment.bottomLeft,
                  child: Padding(
                    padding:
                        const EdgeInsets.only(left: 15, bottom: 8, right: 15),
                    child: Text(
                      capitalizeFirstLetter(title),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(color: Colors.white, fontSize: 18),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
