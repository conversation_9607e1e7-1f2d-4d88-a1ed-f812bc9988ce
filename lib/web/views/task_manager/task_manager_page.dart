import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/web/models/task_model.dart';
import 'package:mass_ibs/web/views/task_manager/widgets/task_table_header.dart';
import 'package:mass_ibs/web/views/task_manager/widgets/task_tile.dart';

import '../../../shared/firebase.dart';
import '../../controller/home_controller.dart';
import '../../shared/methods.dart';
import '../../shared/router.dart';
import '../../shared/theme.dart';
import '../common/page_header.dart';

class TaskManagerPage extends StatefulWidget {
  const TaskManagerPage({super.key});

  @override
  State<TaskManagerPage> createState() => _TaskManagerPageState();
}

class _TaskManagerPageState extends State<TaskManagerPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: primaryColor.withValues(alpha: 0.01),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PageHeaderWithButton(
              title: 'Field Manager',
              button: true,
              buttonName: 'New',
              icon: CupertinoIcons.add,
              onPressed: () {
                addTaskForm(context, null);
              },
            ),
            const SizedBox(height: 20),
            TaskTableHeader(),
            const SizedBox(height: 20),
            GetBuilder<HomeCtrl>(builder: (homeController) {
              return ListView.separated(
                shrinkWrap: true,
                itemCount: homeController.tasks.length,
                itemBuilder: (context, index) {
                  return InkWell(
                    highlightColor: Colors.transparent,
                    overlayColor:
                        const WidgetStatePropertyAll(Colors.transparent),
                    hoverColor: Colors.transparent,
                    onTap: () => context.push(
                      '${WebRoutes.task}/${homeController.tasks[index].docId}?name=${Uri.encodeComponent(homeController.tasks[index].taskName)}',
                    ),
                    child: InkWell(
                      highlightColor: Colors.transparent,
                      overlayColor:
                          const WidgetStatePropertyAll(Colors.transparent),
                      hoverColor: Colors.transparent,
                      onTap: () => context.push(
                        '${WebRoutes.task}/${homeController.tasks[index].docId}?name=${Uri.encodeComponent(homeController.tasks[index].taskName)}',
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                        ),
                        child: TaskTile(
                          index: index,
                          taskModel: homeController.tasks[index],
                        ),
                      ),
                    ),
                  );
                },
                separatorBuilder: (BuildContext context, int index) {
                  return Divider(
                    color: primaryColor,
                    thickness: 0.2,
                  );
                },
              );
            })
          ],
        ),
      ),
    );
  }
}

Future<dynamic> addTaskForm(BuildContext context, TaskModel? task) {
  bool loading = false;
  bool isVehicle = false;

  final formKey = GlobalKey<FormState>();
  TextEditingController taskController = TextEditingController();

  if (task != null) {
    isVehicle = task.isVehicle ?? false;
    taskController.text = task.taskName;
  }

  return showDialog(
    context: context,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState2) {
          return AlertDialog(
            contentPadding:
                const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
            title: Text(task != null ? "Edit Field" : "Add Field"),
            content: Form(
              key: formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 280,
                    child: TextFormField(
                      controller: taskController,
                      cursorHeight: 20,
                      decoration: inpDecor().copyWith(labelText: 'Field Name'),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a field name';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        const Text('Vehicle Document'),
                        const SizedBox(width: 10),
                        Transform.scale(
                          scale: 0.7,
                          child: CupertinoSwitch(
                            value: isVehicle,
                            onChanged: (bool newValue) {
                              setState2(() {
                                isVehicle = newValue;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            actions: loading
                ? [
                    const Center(
                      child: SizedBox(
                        height: 25,
                        width: 25,
                        child: CircularProgressIndicator(strokeWidth: 2.5),
                      ),
                    )
                  ]
                : [
                    TextButton(
                      onPressed: () async {
                        final isValid =
                            formKey.currentState?.validate() ?? false;

                        if (!isValid) {
                          return;
                        }

                        try {
                          setState2(() {
                            loading = true;
                          });

                          final data = {
                            'taskName': taskController.text.trim(),
                            'isVehicle': isVehicle,
                            'createdAt': task != null
                                ? task.createdAt
                                : FieldValue.serverTimestamp(),
                          };

                          await addTask(task, data);

                          if (context.mounted) {
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text("Field Added"),
                                duration: Duration(seconds: 2),
                              ),
                            );
                          }
                        } catch (e, stack) {
                          debugPrint("❌ Error in task add/update: $e");
                          debugPrint("🧵 Stacktrace: $stack");

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text("Something went wrong: $e")),
                          );
                        } finally {
                          setState2(() {
                            loading = false;
                          });
                        }
                      },
                      child: const Text("Add Field"),
                    ),
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text("Cancel"),
                    ),
                  ],
          );
        },
      );
    },
  );
}

Future<void> addTask(TaskModel? task, Map<String, dynamic> data) async {
  try {
    final userCollection = FBFireStore.tasks;
    if (task == null) {
      await userCollection.add(data);
      debugPrint("Document added successfully");
    } else {
      await userCollection.doc(task.docId).update(data);
    }
  } catch (e, stack) {
    debugPrint("🔥 Critical Firestore Error: ${e.toString()}");
    debugPrint("Stack trace: $stack");
    rethrow;
  }
}
