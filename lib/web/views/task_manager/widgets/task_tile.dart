import 'package:flutter/material.dart';
import 'package:mass_ibs/web/models/task_model.dart';
import 'package:mass_ibs/web/views/task_manager/task_manager_page.dart';

import '../../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../../shared/theme.dart';

class TaskTile extends StatefulWidget {
  const TaskTile({super.key, required this.index, required this.taskModel});

  final int index;
  final TaskModel? taskModel;

  @override
  State<TaskTile> createState() => _TaskTileState();
}

class _TaskTileState extends State<TaskTile> {
  @override
  Widget build(BuildContext context) {
    return widget.taskModel != null
        ? Row(
            children: [
              SizedBox(
                  width: 80,
                  child: Text(
                    (widget.index + 1).toString(),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  )),
              const SizedBox(width: 5),
              Expanded(
                child: Text(
                  capitalizeFirstLetter(widget.taskModel!.taskName),
                  style: TextStyle(fontSize: 16),
                ),
              ),
              const SizedBox(width: 16),
              if (widget.taskModel!.taskName.toLowerCase() != "insurance" &&
                  widget.taskModel!.taskName.toLowerCase() != "vehicle fitness" &&
                  widget.taskModel!.taskName.toLowerCase() != "vehicle permit") ...[
                InkWell(
                  highlightColor: Colors.transparent,
                  overlayColor:
                      const WidgetStatePropertyAll(Colors.transparent),
                  hoverColor: Colors.transparent,
                  // highlightColor: Colors.transparent,
                  // hoverColor: Colors.transparent,
                  onTap: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        bool loading = false;
                        return StatefulBuilder(
                          builder: (context, setState2) {
                            return AlertDialog(
                              title: const Text("Alert"),
                              content:
                                  const Text("Are you sure you want to delete"),
                              actions: loading
                                  ? [
                                      const Center(
                                        child: SizedBox(
                                          height: 25,
                                          width: 25,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2.5,
                                          ),
                                        ),
                                      )
                                    ]
                                  : [
                                      TextButton(
                                          onPressed: () async {
                                            try {
                                              setState2(() {
                                                loading = true;
                                              });
                                              try {
                                                await FBFireStore.tasks
                                                    .doc(
                                                        widget.taskModel?.docId)
                                                    .delete();
                                              } on Exception catch (e) {
                                                debugPrint(e.toString());
                                              }
                                              const snackBar = SnackBar(
                                                  content: Text(
                                                      "Task deleted successfully"));
                                              if (context.mounted) {
                                                Navigator.of(context).pop();
                                                setState2(() {
                                                  loading = false;
                                                });
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(snackBar);
                                              }
                                            } catch (e) {
                                              debugPrint(e.toString());

                                              if (context.mounted) {
                                                setState2(() {
                                                  loading = false;
                                                });
                                                Navigator.of(context).pop();
                                              }
                                            }
                                          },
                                          child: const Text('Yes')),
                                      TextButton(
                                          onPressed: () {
                                            Navigator.of(context).pop();
                                          },
                                          child: const Text('No')),
                                    ],
                            );
                          },
                        );
                      },
                    );
                  },
                  child: const Icon(Icons.delete, color: primaryColor),
                ),
              ],
              const SizedBox(width: 16),
              InkWell(
                highlightColor: Colors.transparent,
                overlayColor: const WidgetStatePropertyAll(Colors.transparent),
                hoverColor: Colors.transparent,
                onTap: () => addTaskForm(context, widget.taskModel),
                child: Icon(
                  Icons.edit,
                  color: primaryColor,
                ),
              ),
              const SizedBox(width: 16),
            ],
          )
        : const SizedBox();
  }
}
