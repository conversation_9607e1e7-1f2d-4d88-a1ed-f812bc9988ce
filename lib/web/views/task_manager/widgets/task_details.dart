import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mass_ibs/web/models/task_model.dart';
import 'package:mass_ibs/web/shared/theme.dart';
import 'package:uuid/uuid.dart';

import '../../../../shared/constants/colors.dart';
import '../../../../shared/firebase.dart';
import '../../../shared/methods.dart';
import '../../common/page_header.dart';

class TaskDetails extends StatefulWidget {
  const TaskDetails({
    super.key,
    required this.taskId,
    required this.taskName,
  });

  final String taskId;
  final String taskName;

  @override
  State<TaskDetails> createState() => _TaskDetailsState();
}

class _TaskDetailsState extends State<TaskDetails> {
  List<TaskDetailModel> fields = [];
  List<TextEditingController> controllers = [];
  var uuid = Uuid();

  @override
  void initState() {
    super.initState();
    loadFields(widget.taskId);
  }

  Future<void> loadFields(String taskId) async {
    try {
      List<TaskDetailModel> fetchedFields = await getFieldsFromTask(taskId);
      setState(() {
        fields = fetchedFields;
        controllers =
            fields.map((f) => TextEditingController(text: f.value)).toList();
      });
    } catch (e) {
      debugPrint("Error loading fields: $e");
    }
  }

  void _showInputDialog(BuildContext context) async {
    await showDialog<String>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Select Input Type'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: Text('Date Picker'),
                onTap: () {
                  Navigator.pop(context, 'DatePicker');
                  _addField("DatePicker");
                },
              ),
              ListTile(
                title: Text('Text Field'),
                onTap: () {
                  Navigator.pop(context, 'TextField');
                  _addField("TextField");
                },
              ),
              ListTile(
                title: Text('Expiry Date'),
                onTap: () {
                  Navigator.pop(context, 'ExpiryDate');
                  _addField("ExpiryDate");
                },
              ),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text("Cancel"),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _addField(String type) {
    String title = 'Input ${fields.length + 1} ($type)';
    TextEditingController controller =
        TextEditingController(text: type != "ExpiryDate" ? '' : 'Expiry Date');

    setState(() {
      controllers.add(controller);
      fields.add(TaskDetailModel(
        fieldId: '',
        type: type == "ExpiryDate" ? "ExpiryDate" : type,
        title: title,
        value: type != "ExpiryDate" ? '' : 'Expiry Date',
        required: true,
      ));
    });
  }

  Widget _buildExpandableList() {
    return ListView.separated(
      shrinkWrap: true,
      itemCount: fields.length,
      itemBuilder: (context, index) {
        final field = fields[index];
        // final TextEditingController controller =
        //     TextEditingController(text: field.value);
        return Theme(
          data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
          child: ExpansionTile(
            childrenPadding:
                const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
            collapsedBackgroundColor: primaryColor.withValues(alpha: 0.1),
            dense: true,
            iconColor: Colors.black,
            backgroundColor: primaryColor.withValues(alpha: 0.01),
            title: Row(
              children: [
                Text(field.title), // Field title
                SizedBox(width: 8),
                Spacer(),
                Transform.scale(
                  scale: 0.7, // Adjust the size of the switch
                  child: CupertinoSwitch(
                    value: field.required,
                    onChanged: (bool newValue) {
                      setState(() {
                        field.required = newValue;
                      });
                    },
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  field.required ? "Required" : "Not Required",
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: field.required ? Colors.green : Colors.red,
                  ),
                ),
                SizedBox(width: 8),
                // Add Delete Button
                IconButton(
                  icon: Icon(Icons.delete, color: Colors.red),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) {
                        bool loading = false;
                        return StatefulBuilder(
                          builder: (context, setState2) {
                            return AlertDialog(
                              title: const Text("Alert"),
                              content:
                                  const Text("Are you sure you want to delete"),
                              actions: loading
                                  ? [
                                      const Center(
                                        child: SizedBox(
                                          height: 25,
                                          width: 25,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2.5,
                                          ),
                                        ),
                                      )
                                    ]
                                  : [
                                      TextButton(
                                          onPressed: () async {
                                            try {
                                              setState2(() {
                                                loading = true;
                                              });
                                              try {
                                                _deleteField(index);
                                              } on Exception catch (e) {
                                                debugPrint(e.toString());
                                              }
                                              const snackBar = SnackBar(
                                                  content: Text(
                                                      "Field deleted successfully"));
                                              if (context.mounted) {
                                                Navigator.of(context).pop();
                                                setState2(() {
                                                  loading = false;
                                                });
                                                ScaffoldMessenger.of(context)
                                                    .showSnackBar(snackBar);
                                              }
                                            } catch (e) {
                                              debugPrint(e.toString());

                                              if (context.mounted) {
                                                setState2(() {
                                                  loading = false;
                                                });
                                                Navigator.of(context).pop();
                                              }
                                            }
                                          },
                                          child: const Text('Yes')),
                                      TextButton(
                                          onPressed: () {
                                            Navigator.of(context).pop();
                                          },
                                          child: const Text('No')),
                                    ],
                            );
                          },
                        );
                      },
                    );
                  },
                ),
              ],
            ),
            children: [
              TextFormField(
                enabled: field.value != "Expiry Date",
                controller: controllers[index],
                onChanged: (value) {
                  _handleTextChange(index, value); // Handle value change
                },
                decoration: inpDecor().copyWith(
                  labelText: 'Enter Field Title',
                  hintText: 'Enter value here...',
                ),
              ),
            ],
          ),
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return SizedBox(height: 8);
      },
    );
  }

  // Handle text field value change
  void _handleTextChange(int index, String newText) {
    setState(() {
      fields[index].value = newText; // Update the value in fields list
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: _buildSaveTextButton(),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            PageHeaderWithButton(
              title: capitalizeFirstLetter(widget.taskName),
              button: true,
              back: true,
              buttonName: 'Add Field',
              icon: CupertinoIcons.add,
              onPressed: () {
                _showInputDialog(context);
              },
            ),
            const SizedBox(height: 20),
            _buildExpandableList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveTextButton() {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppInsets.s4),
        ),
        padding: EdgeInsets.all(AppInsets.s16),
      ),
      onPressed: () {
        if (fields.where((element) => element.value == '').isNotEmpty) {
          const snackBar = SnackBar(
            content: Text("Empty Fields!!"),
            duration: Duration(seconds: 2),
          );
          ScaffoldMessenger.of(context).showSnackBar(snackBar);
          return;
        }
        addFieldsToTask(widget.taskId, fields);
      },
      icon: Icon(
        Icons.save,
        color: ColorConstants.white,
        size: AppInsets.s24,
      ),
      label: Text(
        'Save',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: AppInsets.s16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Future<List<TaskDetailModel>> getFieldsFromTask(String taskId) async {
    try {
      final taskDocSnap = await FBFireStore.tasks.doc(taskId).get();

      if (!taskDocSnap.exists) {
        debugPrint("Task with ID $taskId not found");
        return [];
      }

      final data = taskDocSnap.data();
      if (data == null || data['fields'] == null) {
        debugPrint("No 'fields' array found in task document");
        return [];
      }

      // Decode list of maps into List<TaskDetailModel>
      return (data['fields'] as List<dynamic>)
          .map((e) =>
              TaskDetailModel.fromJson(e['title'], e as Map<String, dynamic>))
          .toList();
    } catch (e, stack) {
      debugPrint("🔥 Error fetching fields from task doc: ${e.toString()}");
      debugPrint("Stack trace: $stack");
      rethrow;
    }
  }

  Future<void> _deleteField(int index) async {
    try {
      final field = fields[index]; // This is a TaskDetailModel

      final taskDoc = FBFireStore.tasks.doc(widget.taskId);

      // Build a plain map that matches exactly what's stored in Firestore
      final fieldMap = {
        'fieldId': field.fieldId,
        'type': field.type,
        'title': field.title,
        'value': field.value,
        'required': field.required,
      };

      // Remove the field from the 'fields' array in the main task doc
      await taskDoc.update({
        'fields': FieldValue.arrayRemove([fieldMap]),
      });

      debugPrint("Field removed from task doc: ${field.fieldId}");

      setState(() {
        fields.removeAt(index);
      });

      const snackBar = SnackBar(
        content: Text("Field deleted successfully"),
        duration: Duration(seconds: 2),
      );
      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    } catch (e) {
      debugPrint("Error deleting field: $e");
    }
  }

  Future<void> addFieldsToTask(
      String taskId, List<TaskDetailModel> fieldsData) async {
    try {
      final taskDoc = FBFireStore.tasks.doc(taskId);

      // Convert all TaskDetailModel objects to JSON maps
      final List<Map<String, dynamic>> fieldsJson = fieldsData.map((field) {
        // Ensure each field has a fieldId
        return {
          'fieldId': field.fieldId.isEmpty ? uuid.v4() : field.fieldId,
          'type': field.type,
          'title': field.value,
          'value': field.value,
          'required': field.required,
        };
      }).toList();

      // Update the task document with the full fields array
      await taskDoc.update({'fields': fieldsJson});

      const snackBar = SnackBar(
        content: Text("Fields saved successfully"),
        duration: Duration(seconds: 2),
      );
      ScaffoldMessenger.of(context).showSnackBar(snackBar);

      debugPrint("Fields updated in main task document for task ID: $taskId");
    } catch (e, stack) {
      debugPrint("🔥 Error saving fields in main doc: $e");
      debugPrint("Stack trace: $stack");
      rethrow;
    }
  }
}
