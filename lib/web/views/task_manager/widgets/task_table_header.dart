// ignore: unnecessary_import
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../shared/theme.dart';
import '../../common/table_header.dart';

class TaskTableHeader extends StatelessWidget {
  const TaskTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: primaryColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 80,
            child: Text(
              'Sr No',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontWeight: FontWeight.w500,
                  letterSpacing: 1.2,
                  color: Colors.white),
            ),
          ),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Field Name')),
          const SizedBox(width: 5),
          Spacer(),
          Opacity(
            opacity: 0,
            child: SizedBox(
              width: 60,
              child: IconButton(
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onPressed: () {},
                icon: const Icon(
                  Icons.delete,
                  color: primaryColor,
                ),
              ),
            ),
          ),
          TableHeaderText(headerName: 'Actions'),
          const SizedBox(width: 36),
        ],
      ),
    );
  }
}
