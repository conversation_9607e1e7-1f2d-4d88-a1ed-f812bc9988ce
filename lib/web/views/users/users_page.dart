import 'dart:async';

import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/shared/firebase.dart';
import 'package:mass_ibs/web/models/user_docs_model.dart';
import 'package:mass_ibs/web/shared/const.dart';
import 'package:mass_ibs/web/views/users/widgets/user_add_edit.dart';

import '../../controller/home_controller.dart';
import '../../models/user_model.dart';
import '../../shared/router.dart';
import '../common/header_search_feild.dart';
import '../common/page_header.dart';
import 'widgets/user_table_header.dart';
import 'widgets/user_tile.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends State<UsersPage> {
  TextEditingController searchController = TextEditingController();
  int currentPage = 1;
  int totalPages = 1;

  void goToPrevious() {
    if (currentPage > 1) {
      setState(() => currentPage--);
    }
  }

  void goToNext() {
    if (currentPage < totalPages) {
      setState(() => currentPage++);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 20),
        child: GetBuilder<HomeCtrl>(builder: (homeController) {
          if (homeController.isUserLoading.value) {
            return Center(
                child: Padding(
              padding: const EdgeInsets.all(24),
              child: CircularProgressIndicator(),
            ));
          }

          List<UserModel> filteredList = homeController.users.where((element) {
            final searchText = searchController.text.toLowerCase();
            final name = element.name.toLowerCase();
            final number = element.number;
            final email = element.email;
            if (searchText.length < 3) {
              return true;
            } else {
              return name.contains(searchText) ||
                  number.contains(searchText) ||
                  email.contains(searchText);
            }
          }).toList();
          totalPages = (filteredList.length / userPerPageLimit).ceil();
          final paginatedList = searchController.text.length < 3
              ? filteredList.sublist(
                  (currentPage - 1) * userPerPageLimit,
                  ((currentPage) * userPerPageLimit) > filteredList.length
                      ? filteredList.length
                      : (currentPage) * userPerPageLimit)
              : filteredList;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              PageHeaderWithButton(
                title: 'Users',
                button: true,
                buttonName: 'New',
                count: "(${Get.find<HomeCtrl>().users.length})",
                icon: CupertinoIcons.add,
                onPressed: false
                    ? () async {
                        final data = await FBFireStore.usersDocs
                            .where('vehicleDoc', isEqualTo: true)
                            .get();
                        for (var element in data.docs) {
                          final doc = UserDocsModel.fromSnapshot(element);
                          if (doc.files.isNotEmpty) {
                            for (var file in doc.files) {
                              final imageRef = FirebaseStorage.instance
                                  .refFromURL(file.fileUrl);
                              // Update metadata with basic content type based on file extension
                              final fileExtension = file.fileUrl
                                  .split('.')
                                  .last
                                  .split('?')
                                  .first
                                  .toLowerCase();
                              final contentType = fileExtension == 'pdf'
                                  ? 'application/pdf'
                                  : 'image/$fileExtension';
                              await imageRef.updateMetadata(SettableMetadata(
                                contentDisposition: 'inline',
                                contentType: contentType,
                              ));
                              //  await FBFireStore.usersDocs
                              //       .doc(element.id)
                              //       .update({
                              //     'fileUrl': element.data()['fileUrl'],
                              //   });
                              print("---qsw");
                            }
                          }
                        }
                      }
                    : false
                        ? () async {
                            try {
                              print("Starting metadata update process...");
                              final data = await FBFireStore.usersDocs
                                  .where('vehicleDoc', isEqualTo: true)
                                  .get();

                              print("Found ${data.docs.length} documents");

                              for (var element in data.docs) {
                                if (element.data().containsKey('fileUrl') &&
                                    element.data()['fileUrl'] != '') {
                                  final fileUrl =
                                      element.data()['fileUrl'] as String;
                                  print("Processing file: $fileUrl");

                                  try {
                                    final imageRef = FirebaseStorage.instance
                                        .refFromURL(fileUrl);

                                    // Get file extension
                                    final fileExtension = fileUrl
                                        .split('.')
                                        .last
                                        .split('?')
                                        .first
                                        .toLowerCase();

                                    print("File extension: $fileExtension");

                                    final contentType = fileExtension == 'pdf'
                                        ? 'application/pdf'
                                        : 'image/$fileExtension';

                                    print("Content type: $contentType");

                                    // Update metadata
                                    final metadata = SettableMetadata(
                                      contentDisposition: 'inline',
                                      contentType: contentType,
                                    );

                                    await imageRef.updateMetadata(metadata);
                                    print(
                                        "✅ Successfully updated metadata for: $fileUrl");
                                  } catch (fileError) {
                                    print(
                                        "❌ Error updating file $fileUrl: $fileError");
                                  }
                                }
                              }
                              print("Metadata update process completed!");
                            } catch (e) {
                              print("❌ General error in metadata update: $e");
                            }
                          }
                        : () {
                            newUserForm(
                              context,
                            );
                          },
              ),
              const SizedBox(height: 20),
              SearchField(
                searchController: searchController,
                onChanged: (p0) async {
                  if (p0.length > 2) {
                    currentPage = 1;
                  }
                  setState(() {});
                },
              ),
              const SizedBox(height: 25),
              const UserTableHeader(),
              ListView.builder(
                shrinkWrap: true,
                itemCount: paginatedList.length,
                itemBuilder: (context, index) {
                  return InkWell(
                    highlightColor: Colors.transparent,
                    overlayColor:
                        const WidgetStatePropertyAll(Colors.transparent),
                    hoverColor: Colors.transparent,
                    onTap: () {
                      context.push(
                        '${WebRoutes.user}/${paginatedList[index].docId}?name=${Uri.encodeComponent(paginatedList[index].name)}?contact=${Uri.encodeComponent(paginatedList[index].number)}',
                      );
                    },
                    child: UserTile(
                      index: index + ((currentPage - 1) * userPerPageLimit),
                      userModel: paginatedList[index],
                    ),
                  );
                },
              ),
              if (searchController.text.length < 3) ...[
                SizedBox(
                  height: 20,
                ),
                Center(
                    child: PaginationArrowButtons(
                        currentPage: currentPage,
                        totalPages: totalPages,
                        onPrevious: () {
                          goToPrevious();
                          setState(() {});
                        },
                        onNext: () {
                          goToNext();
                          setState(() {});
                        })),
              ]
            ],
          );
        }));
  }

  Future<dynamic> newUserForm(
    BuildContext context,
  ) {
    return showDialog(
      context: context,
      builder: (context) {
        return UserAddEdit();
      },
    );
  }
}

class PaginationArrowButtons extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final VoidCallback? onPrevious;
  final VoidCallback? onNext;

  const PaginationArrowButtons({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.onPrevious,
    this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    final isFirstPage = currentPage <= 1;
    final isLastPage = currentPage >= totalPages;
    Widget arrowButton({
      required IconData icon,
      required VoidCallback? onTap,
      bool disabled = false,
    }) {
      return GestureDetector(
        onTap: disabled ? null : onTap,
        child: Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: disabled ? Colors.grey.shade300 : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              if (!disabled)
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
            ],
            border: Border.all(
              color: disabled ? Colors.grey.shade400 : Colors.black12,
            ),
          ),
          child: Icon(
            icon,
            color: disabled ? Colors.grey : Colors.black,
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        arrowButton(
          icon: Icons.chevron_left,
          onTap: onPrevious,
          disabled: isFirstPage,
        ),
        SizedBox(
          width: 20,
        ),
        Text(
          '$currentPage / $totalPages',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        SizedBox(
          width: 20,
        ),
        arrowButton(
          icon: Icons.chevron_right,
          onTap: onNext,
          disabled: isLastPage,
        ),
      ],
    );
  }
}
