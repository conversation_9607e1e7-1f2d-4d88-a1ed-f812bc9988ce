import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/web/controller/home_controller.dart';
import 'package:mass_ibs/web/views/users/widgets/user_add_edit.dart';

import '../../../../shared/firebase.dart';
import '../../../models/user_model.dart';
import '../../../shared/methods.dart';
import '../../../shared/router.dart' show WebRoutes;
import '../../../shared/theme.dart';

class UserTile extends StatefulWidget {
  const UserTile({super.key, required this.index, required this.userModel});

  final int index;
  final UserModel? userModel;

  @override
  State<UserTile> createState() => _UserTileState();
}

class _UserTileState extends State<UserTile> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // print("---user--${widget.userModel?.name}----$loading");
    return (widget.userModel != null)
        ? Container(
            decoration: widget.index % 2 != 0
                ? BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    // color: Colors.grey[200],
                    color: primaryColor.withValues(alpha: 0.1))
                : null,
            child: Row(
              children: [
                SizedBox(
                    width: 80,
                    child: Text((widget.index + 1).toString(),
                        textAlign: TextAlign.center)),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(
                  widget.userModel?.createdAt != null
                      ? widget.userModel!.createdAt!.toDate().convertToDDMMYY()
                      : "",
                )),
                const SizedBox(width: 5),
                Expanded(
                    child: Text(capitalizeFirstLetter(widget.userModel!.name))),
                const SizedBox(width: 5),
                Expanded(child: Text(widget.userModel!.number)),
                const SizedBox(width: 5),
                Expanded(child: Text(widget.userModel!.email)),
                // const SizedBox(width: 5),
                // Expanded(child: Text(userDocCount.toString())),
                // child: Text(widget.userModel!.documents.length.toString())),
                const SizedBox(width: 5),
                SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {
                      if (widget.userModel != null) {
                        newUserForm(context, widget.userModel!);
                      }
                      UserAddEdit(user: widget.userModel);
                    },
                    icon: const Icon(Icons.edit, color: primaryColor),
                  ),
                ),
                // const SizedBox(width: 5),
                SizedBox(
                  width: 60,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    onPressed: () {
                      bool authorized = false;
                      showDialog(
                        context: context,
                        builder: (context) {
                          bool loading = false;
                          return StatefulBuilder(
                            builder: (context, setState2) {
                              return !authorized
                                  ? AlertDialog(
                                      title: Text('Enter password!!'),
                                      content: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 10.0),
                                        child: TextFormField(
                                          onFieldSubmitted: (value) {
                                            if (value.trim() ==
                                                (Get.find<HomeCtrl>()
                                                        .setting
                                                        ?.password ??
                                                    "")) {
                                              authorized = true;
                                              setState2(() {});
                                            } else {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                SnackBar(
                                                    content: Text(
                                                        "Wrong password!!")),
                                              );
                                            }
                                          },
                                          cursorHeight: 20,
                                          decoration: inpDecor()
                                              .copyWith(labelText: 'Password'),
                                        ),
                                      ),
                                    )
                                  : AlertDialog(
                                      title: const Text("Alert"),
                                      content: const Text(
                                          "Are you sure you want to delete"),
                                      actions: loading
                                          ? [
                                              const Center(
                                                child: SizedBox(
                                                  height: 25,
                                                  width: 25,
                                                  child:
                                                      CircularProgressIndicator(
                                                    strokeWidth: 2.5,
                                                  ),
                                                ),
                                              )
                                            ]
                                          : [
                                              TextButton(
                                                  onPressed: () async {
                                                    try {
                                                      setState2(() {
                                                        loading = true;
                                                      });
                                                      try {
                                                        final HttpsCallable
                                                            callable =
                                                            FBFunctions.ff
                                                                .httpsCallable(
                                                                    'deleteUser');
                                                        await callable.call({
                                                          'uid': widget
                                                              .userModel?.userId
                                                        });
                                                      } on Exception catch (e) {
                                                        debugPrint(
                                                            e.toString());
                                                      }
                                                      const snackBar = SnackBar(
                                                          content: Text(
                                                              "User deleted successfully"));
                                                      if (context.mounted) {
                                                        Navigator.of(context)
                                                            .pop();
                                                        setState2(() {
                                                          loading = false;
                                                        });
                                                        ScaffoldMessenger.of(
                                                                context)
                                                            .showSnackBar(
                                                                snackBar);
                                                      }
                                                    } catch (e) {
                                                      debugPrint(e.toString());

                                                      if (context.mounted) {
                                                        setState2(() {
                                                          loading = false;
                                                        });
                                                        Navigator.of(context)
                                                            .pop();
                                                      }
                                                    }
                                                  },
                                                  child: const Text('Yes')),
                                              TextButton(
                                                  onPressed: () {
                                                    Navigator.of(context).pop();
                                                  },
                                                  child: const Text('No')),
                                            ],
                                    );
                            },
                          );
                        },
                      );
                    },
                    icon: const Icon(Icons.delete, color: primaryColor),
                  ),
                ),
                const SizedBox(width: 5),
                InkWell(
                  highlightColor: Colors.transparent,
                  overlayColor:
                      const WidgetStatePropertyAll(Colors.transparent),
                  hoverColor: Colors.transparent,
                  onTap: () => context.push(
                    '${WebRoutes.user}/${widget.userModel?.docId}?name=${Uri.encodeComponent(widget.userModel!.name)}?contact=${Uri.encodeComponent(widget.userModel!.number)}',
                  ),
                  child: Icon(
                    Icons.arrow_forward,
                    color: primaryColor,
                  ),
                ),
                const SizedBox(width: 16),
              ],
            ),
          )
        : const SizedBox();
  }

  Future<dynamic> newUserForm(BuildContext context, UserModel user) {
    return showDialog(
      context: context,
      builder: (context) {
        return UserAddEdit(
          user: user,
        );
      },
    );
  }
}
