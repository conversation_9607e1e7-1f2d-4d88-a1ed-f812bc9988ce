// ignore: unnecessary_import
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../shared/theme.dart';
import '../../common/table_header.dart';

class UserTableHeader extends StatelessWidget {
  const UserTableHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: primaryColor,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 80,
            child: Text(
              'Sr No',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontWeight: FontWeight.w500,
                  letterSpacing: 1.2,
                  color: Colors.white),
            ),
          ),
          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Date')),

          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'User Name')),

          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Mobile Number')),

          const SizedBox(width: 5),
          const Expanded(child: TableHeaderText(headerName: 'Email')),

          // const SizedBox(width: 5),
          // const Expanded(child: TableHeaderText(headerName: 'Documents')),

          // const Expanded(child: TableHeaderText(headerName: '')),
          // const SizedBox(width: 5),

          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //     width: 60,
          //     child: IconButton(
          //       highlightColor: Colors.transparent,
          //       hoverColor: Colors.transparent,
          //       onPressed: () {},
          //       icon: const Icon(
          //         CupertinoIcons.pencil,
          //         size: 22,
          //       ),
          //     ),
          //   ),
          // ),
          // Opacity(
          //   opacity: 0,
          //   child: SizedBox(
          //       width: 60,
          //       child: Transform.scale(
          //         scale: .65,
          //         child: CupertinoSwitch(
          //           value: true,
          //           onChanged: (value) {},
          //         ),
          //       )),
          // ),
          const SizedBox(width: 5),
          Opacity(
            opacity: 0,
            child: SizedBox(
              width: 60,
              child: IconButton(
                highlightColor: Colors.transparent,
                hoverColor: Colors.transparent,
                onPressed: () {},
                icon: const Icon(
                  Icons.delete,
                  color: primaryColor,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 50,
          ),
        ],
      ),
    );
  }
}
