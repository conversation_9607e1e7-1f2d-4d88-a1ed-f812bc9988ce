import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/utils.dart';
import 'package:go_router/go_router.dart';
import 'package:mass_ibs/shared/constants/colors.dart';
import 'package:mass_ibs/shared/constants/svgs_constants.dart';
import 'package:mass_ibs/shared/firebase.dart';
import 'package:mass_ibs/shared/widgets/universal_image.dart';
import 'package:mass_ibs/web/models/user_docs_model.dart';
import 'package:mass_ibs/web/models/vehicle_model.dart';
import 'package:mass_ibs/web/shared/methods.dart';
import 'package:mass_ibs/web/shared/router.dart';
import 'package:mass_ibs/web/shared/theme.dart';
import 'package:mass_ibs/web/views/common/delete_alert.dart';

class UserVehicleCard extends StatefulWidget {
  const UserVehicleCard(
      {super.key,
      required this.userId,
      required this.vehicle,
      required this.onVehicleFormCall});
  final String userId;
  final VehicleModel vehicle;
  final Function(BuildContext context) onVehicleFormCall;
  @override
  State<UserVehicleCard> createState() => _UserVehicleCardState();
}

class _UserVehicleCardState extends State<UserVehicleCard> {
  bool loading = true;
  int vehicleDocCount = 0;
  UserDocsModel? insuranceDoc;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    vehicleDocs();
  }

  vehicleDocs() async {
    try {
      vehicleDocCount = (await FBFireStore.usersDocs
                  .where('vehicleDoc', isEqualTo: true)
                  .where('vehicleId', isEqualTo: widget.vehicle.docId)
                  .where('uId', isEqualTo: widget.userId)
                  .count()
                  .get())
              .count ??
          0;
      insuranceDoc = (await FBFireStore.usersDocs
              .where('vehicleDoc', isEqualTo: true)
              .where('vehicleId', isEqualTo: widget.vehicle.docId)
              .where('uId', isEqualTo: widget.userId)
              .where('docName', isEqualTo: 'Insurance')
              .limit(1)
              .get())
          .docs
          .map((e) => UserDocsModel.fromSnapshot(e))
          .firstOrNull;
    } catch (e) {
      // TODO
      debugPrint("-- ${e.toString()}");
    }
    loading = false;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return loading
        ? CircularProgressIndicator()
        : Stack(
            children: [
              InkWell(
                highlightColor: Colors.transparent,
                overlayColor: const WidgetStatePropertyAll(Colors.transparent),
                hoverColor: Colors.transparent,
                onTap: () {
                  context.push(
                    '${WebRoutes.vehicle}/${widget.vehicle.docId}',
                    extra: widget.vehicle,
                  );
                },
                child: Container(
                  constraints: const BoxConstraints(
                    maxWidth: 200,
                    maxHeight: 144,
                    minWidth: 200,
                    minHeight: 144,
                  ),
                  padding: const EdgeInsets.symmetric(vertical: AppInsets.s12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    border: Border.all(
                      color: ColorConstants.borderColor,
                      width: AppInsets.s1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppInsets.pageMargin),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(AppInsets.s6),
                          width: AppInsets.s36,
                          height: AppInsets.s36,
                          decoration: BoxDecoration(
                            color: ColorConstants.grayBackGroundColor,
                            borderRadius: BorderRadius.circular(AppInsets.s4),
                          ),
                          child: UniversalImage(
                            SvgsConstants.car,
                            height: AppInsets.s24,
                            width: AppInsets.s24,
                          ),
                        ),
                        const SizedBox(height: AppInsets.s8),
                        Text(
                          widget.vehicle.vehicleNumber.toUpperCase(),
                          style: TextStyle(
                            fontSize: AppInsets.s16,
                            color: ColorConstants.black,
                            fontWeight: FontWeight.w500,
                            fontFamily: AppFonts.lexend,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          // 'Documents: ${ widget.vehicle.vehicleDocuments.length}',
                          'Documents: $vehicleDocCount',
                          style: TextStyle(
                            fontSize: AppInsets.s14,
                            color: ColorConstants.textFieldHintColor,
                            fontWeight: FontWeight.w400,
                            fontFamily: AppFonts.lexend,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          // 'Documents: ${ widget.vehicle.vehicleDocuments.length}',
                          (insuranceDoc?.fieldData
                                      ?.firstWhereOrNull((field) =>
                                          field.title.toLowerCase() ==
                                          'expiry date')
                                      ?.value as Timestamp?)
                                  ?.toDate()
                                  .goodDayDate() ??
                              'N/A',
                          style: TextStyle(
                            fontSize: AppInsets.s14,
                            color: ColorConstants.textFieldHintColor,
                            fontWeight: FontWeight.w400,
                            fontFamily: AppFonts.lexend,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      widget.onVehicleFormCall(context);
                    } else if (value == 'delete') {
                      showDialog(
                          context: context,
                          builder: (context) {
                            return DeleteAlert(
                              yesPressed: () async {
                                try {
                                  await FBFireStore.vehicles
                                      .doc(widget.vehicle.docId)
                                      .delete();
                                  await FBFireStore.usersDocs
                                      .where('vehicleId',
                                          isEqualTo: widget.vehicle.docId)
                                      .get()
                                      .then((value) async {
                                    for (var element in value.docs) {
                                      await FBFireStore.usersDocs
                                          .doc(element.id)
                                          .delete();
                                    }
                                  });
                                } on Exception catch (e) {
                                  debugPrint(e.toString());
                                }
                              },
                            );
                          });
                    }
                  },
                  itemBuilder: (BuildContext context) =>
                      <PopupMenuEntry<String>>[
                    const PopupMenuItem<String>(
                      value: 'edit',
                      child: Text('Edit'),
                    ),
                    const PopupMenuItem<String>(
                      value: 'delete',
                      child: Text('Delete'),
                    ),
                  ],
                  icon: const Icon(
                    Icons.more_vert,
                    color: primaryColor,
                  ),
                ),
              ),
            ],
          );
  }
}
