import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:mass_ibs/modules/add_vehicle/add_vehicle_controller.dart';
import 'package:mass_ibs/shared/constants/colors.dart';
import 'package:mass_ibs/shared/firebase.dart';
import 'package:mass_ibs/web/controller/home_controller.dart';
import 'package:mass_ibs/web/models/vehicle_model.dart';
import 'package:mass_ibs/web/shared/methods.dart';
import 'package:mass_ibs/web/views/common/page_header.dart';
import 'package:mass_ibs/web/views/users/widgets/user_vehicle_card.dart';

class UserVehicleList extends StatefulWidget {
  const UserVehicleList({super.key, required this.userId});
  final String userId;
  @override
  State<UserVehicleList> createState() => _UserVehicleListState();
}

class _UserVehicleListState extends State<UserVehicleList> {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        PageHeaderWithButton(
          title: 'My Vehicles',
          button: true,
          back: false,
          buttonName: 'Add Vehicles',
          icon: CupertinoIcons.add,
          onPressed: () {
            addVehicleForm(
              context,
              null,
              widget.userId,
            );
          },
        ),
        const SizedBox(height: AppInsets.s16),
        GetBuilder<HomeCtrl>(builder: (homeController) {
          if (homeController.isVehiclesLoading.value) {
            return Center(
                child: Padding(
              padding: const EdgeInsets.all(24),
              child: CircularProgressIndicator(),
            ));
          }

          List<VehicleModel> vehiclesList = homeController.vehicles
              .where((vehicle) => vehicle.userId == widget.userId)
              .toList();
          vehiclesList.sort((a, b) => (b.createdAt ?? Timestamp.now())
              .compareTo(a.createdAt ?? Timestamp.now()));
          return vehiclesList.isNotEmpty
              ? Wrap(
                  key: ValueKey(DateTime.now()),
                  spacing: AppInsets.s16,
                  runSpacing: AppInsets.s16,
                  children: List.generate(vehiclesList.length, (index) {
                    return UserVehicleCard(
                      userId: widget.userId,
                      vehicle: vehiclesList[index],
                      onVehicleFormCall: (context) {
                        addVehicleForm(
                          context,
                          vehiclesList[index],
                          widget.userId,
                        );
                      },
                    );
                  }),
                )
              : const Center(
                  child: Text(
                    'No Vehicles Found',
                    style: TextStyle(
                      fontSize: 16,
                    ),
                  ),
                );
        }),
      ],
    );
  }

  Future<void> addVehicle(Map<String, dynamic> data) async {
    try {
      await FBFireStore.vehicles.add(data);
    } catch (e, stack) {
      debugPrint("🔥 Critical Firestore Error: ${e.toString()}");
      debugPrint("Stack trace: $stack");
      rethrow;
    }
  }

  Future<dynamic> addVehicleForm(
    BuildContext context,
    VehicleModel? vehicle,
    String userId,
  ) {
    bool loading = false;
    VehicleType selectedVehicleType;
    TextEditingController vehicleNameController = TextEditingController();
    TextEditingController vehicleNumberController = TextEditingController();
    TextEditingController chassisNumberController = TextEditingController();
    TextEditingController vehicleTypeController = TextEditingController();
    if (vehicle != null) {
      vehicleNameController.text = vehicle.vehicleName.toUpperCase();
      vehicleNumberController.text = vehicle.vehicleNumber.toUpperCase();
      chassisNumberController.text = vehicle.chassisNumber;
      vehicleTypeController.text = vehicle.vehicleType;
    }
    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            contentPadding:
                const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
            title: Text(vehicle != null ? "Edit Vehicle" : "Add vehicle"),
            content: SizedBox(
              width: 280,
              child: Form(
                key: formKey, // 👈 Attach form key
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: vehicleNameController,
                      cursorHeight: 20,
                      decoration:
                          inpDecor().copyWith(labelText: 'Vehicle Name*'),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Vehicle name is required';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: vehicleNumberController,
                      cursorHeight: 20,
                      decoration:
                          inpDecor().copyWith(labelText: 'Vehicle Number*'),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Vehicle number is required';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: loading
                ? [
                    const Center(
                      child: SizedBox(
                        height: 25,
                        width: 25,
                        child: CircularProgressIndicator(
                          strokeWidth: 2.5,
                        ),
                      ),
                    )
                  ]
                : [
                    TextButton(
                      onPressed: () async {
                        try {
                          if (!formKey.currentState!.validate()) {
                            return;
                          }

                          setState2(() {
                            loading = true;
                          });

                          final data = {
                            'userId': userId,
                            'vehicleName':
                                vehicleNameController.text.trim().toUpperCase(),
                            'chassisNumber': chassisNumberController.text
                                .trim()
                                .toUpperCase(),
                            'vehicleNumber': vehicleNumberController.text
                                .trim()
                                .toUpperCase(),
                            'vehicleType':
                                vehicleTypeController.text.trim().toUpperCase(),
                            "isDeleted": false,
                            'createdAt': vehicle != null
                                ? vehicle.createdAt
                                : FieldValue.serverTimestamp(),
                          };
                          if (vehicle != null) {
                            await updateVehicle(vehicle.docId, data);
                          } else {
                            // Add new vehicle
                            await addVehicle(data);
                          }
                          const snackBar = SnackBar(
                            content: Text("Vehicle Added"),
                            duration: Duration(seconds: 2),
                          );
                          if (context.mounted) {
                            vehicleNameController.clear();
                            vehicleNumberController.clear();
                            vehicleTypeController.clear();
                            chassisNumberController.clear();
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context)
                                .showSnackBar(snackBar);
                          }
                        } catch (e, stack) {
                          debugPrint("❌ Error in user add/update: $e");
                          debugPrint("🧵 Stacktrace: $stack");
                          // Optionally show an error to the user
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text("Something went wrong: $e")),
                          );
                        } finally {
                          setState2(() {
                            loading = false;
                          });
                        }
                      },
                      child: Text(vehicle != null ? "Update" : "Add"),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: const Text("Cancel"),
                    ),
                  ],
          );
        });
      },
    );
  }

  Future<void> updateVehicle(String docId, Map<String, dynamic> data) async {
    try {
      await FBFireStore.vehicles.doc(docId).update(data);
    } catch (e, stack) {
      debugPrint("🔥 Firestore Update Error: ${e.toString()}");
      debugPrint("Stack trace: $stack");
      rethrow;
    }
  }
}
