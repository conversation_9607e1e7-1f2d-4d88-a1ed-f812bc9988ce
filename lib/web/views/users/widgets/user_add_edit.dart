import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:mass_ibs/modules/home/<USER>';
import 'package:mass_ibs/shared/firebase.dart';
import 'package:mass_ibs/web/controller/home_controller.dart';
import 'package:mass_ibs/web/models/user_model.dart';
import 'package:mass_ibs/web/shared/methods.dart';
import 'package:uuid/uuid.dart';

class UserAddEdit extends StatefulWidget {
  const UserAddEdit({super.key, this.user});
  final UserModel? user;
  @override
  State<UserAddEdit> createState() => _UserAddEditState();
}

class _UserAddEditState extends State<UserAddEdit> {
  bool loading = false;
  TextEditingController nameCtrl = TextEditingController();
  TextEditingController numberCtrl = TextEditingController();
  TextEditingController emailCtrl = TextEditingController();
  RxBool errortextNum = RxBool(false);
  RxBool errortextEmail = RxBool(false);
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.user != null) {
      nameCtrl.text = widget.user?.name ?? "";
      numberCtrl.text = widget.user?.number ?? "";
      emailCtrl.text = widget.user?.email ?? "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      contentPadding: const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
      title: Text(widget.user != null ? "Edit User" : "Add User"),
      content: SizedBox(
        width: 280,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: nameCtrl,
              cursorHeight: 20,
              decoration: inpDecor().copyWith(labelText: 'Name'),
            ),
            const SizedBox(height: 20),
            TextFormField(
              // enabled: widget.user == null,
              controller: numberCtrl,
              cursorHeight: 20,
              decoration: inpDecor().copyWith(labelText: 'Mobile Number'),
            ),
            const SizedBox(height: 5),
            if (errortextNum.value)
              Text(
                "Number already exists",
                style: TextStyle(fontSize: 12, color: Colors.red),
              ),
            const SizedBox(height: 20),
            TextFormField(
              controller: emailCtrl,
              cursorHeight: 20,
              decoration: inpDecor().copyWith(labelText: 'Email'),
            ),
            const SizedBox(height: 5),
            if (errortextEmail.value)
              Text(
                "Email already exists",
                style: TextStyle(fontSize: 12, color: Colors.red),
              ),
            const SizedBox(height: 20),
          ],
        ),
      ),
      actions: loading
          ? [
              const Center(
                child: SizedBox(
                  height: 25,
                  width: 25,
                  child: CircularProgressIndicator(
                    strokeWidth: 2.5,
                  ),
                ),
              )
            ]
          : [
              TextButton(
                onPressed: () async {
                  try {
                    if (numberCtrl.text.isEmpty ||
                        (numberCtrl.text.contains('+91')
                            ? (numberCtrl.text.length != 13)
                            : (numberCtrl.text.length != 10)) ||
                        nameCtrl.text.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text("Empty Fields!!")),
                      );
                      return;
                    }
                    final phone =
                        numberCtrl.text.trim().substring(0, 3) == '+91'
                            ? numberCtrl.text.trim()
                            : '+91${numberCtrl.text.trim()}';

                    if (Get.find<HomeCtrl>()
                            .users
                            .map((e) => e.number)
                            .toList()
                            .contains(phone) &&
                        widget.user?.number != phone) {
                      // ScaffoldMessenger.of(context).showSnackBar(
                      //   SnackBar(
                      //       content: Text("$phone number already exists!!")),
                      // );
                      errortextNum = true.obs;
                      setState(() {});
                      return;
                    }

                    if ((Get.find<HomeCtrl>()
                                .users
                                .map((e) => e.email.trim())
                                .toList()
                                .contains(emailCtrl.text.trim()) &&
                            widget.user?.email != emailCtrl.text.trim()) &&
                        emailCtrl.text.trim() != '') {
                      // ScaffoldMessenger.of(context).showSnackBar(
                      //   SnackBar(
                      //       content: Text(
                      //           "${emailCtrl.text.trim()} email already exists!!")),
                      // );
                      errortextEmail = true.obs;
                      setState(() {});
                      return;
                    }
                    setState(() {
                      loading = true;
                    });
                    errortextEmail = false.obs;
                    errortextNum = false.obs;
                    if (widget.user == null) {
                      final uuid = Uuid();
                      final uid = uuid.v4();

                      final data = {
                        'name': nameCtrl.text.toLowerCase().trim(),
                        'phoneNumber': phone,
                        'email': emailCtrl.text.trim(),
                        'firebaseToken': '',
                        // 'createdAt': user != null
                        //     ? user.createdAt
                        //     : FieldValue.serverTimestamp(),
                      };
                      await addUser(data);
                    } else {
                      final data = {
                        'uid': widget.user?.userId,
                        'name': nameCtrl.text.toLowerCase().trim(),
                        'phoneNumber': phone,
                        'email': emailCtrl.text.trim(),

                        // 'createdAt': user != null
                        //     ? user.createdAt
                        //     : FieldValue.serverTimestamp(),
                      };
                      if (phone != widget.user?.number) {
                        final data = await FBFireStore.usersDocs
                            .where('uId', isEqualTo: widget.user?.userId)
                            .get();
                        if (data.docs.isNotEmpty) {
                          for (var element in data.docs) {
                            await FBFireStore.usersDocs
                                .doc(element.id)
                                .update({'userContact': phone});
                          }
                        }
                      }
                      final HttpsCallable callable = FirebaseFunctions.instance
                          .httpsCallable('updateUser');
                      await callable.call(data);

                      // await FBFireStore.users
                      //     .doc(widget.user?.userId)
                      //     .update(data);
                    }
                    var snackBar = SnackBar(
                      content: widget.user == null
                          ? Text("User Added")
                          : Text("User Updated"),
                      duration: Duration(seconds: 2),
                    );
                    if (context.mounted) {
                      nameCtrl.clear();
                      numberCtrl.clear();
                      emailCtrl.clear();
                      Navigator.of(context).pop();
                      ScaffoldMessenger.of(context).showSnackBar(snackBar);
                    }
                  } catch (e, stack) {
                    debugPrint("❌ Error in user add/update: $e");
                    debugPrint("🧵 Stacktrace: $stack");
                    // Optionally show an error to the user
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text("Something went wrong: $e")),
                    );
                  } finally {
                    setState(() {
                      loading = false;
                    });
                  }
                },
                child: widget.user == null ? Text("Add") : Text("Update"),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text("Cancel"),
              ),
            ],
    );
  }

  Future<void> addUser(Map<String, dynamic> data) async {
    try {
      final HttpsCallable callable = FBFunctions.ff.httpsCallable('createUser');
      await callable.call(data);
    } catch (e, stack) {
      debugPrint("🔥 Critical Firestore Error: ${e.toString()}");
      debugPrint("Stack trace: $stack");
      rethrow;
    }
  }
}
