import 'package:flutter/material.dart';

import '../../shared/theme.dart';

class FormHeaderTile extends StatelessWidget {
  const FormHeaderTile({
    super.key,
    required this.title,
    this.button = false,
    this.divider = true,
    this.buttonName,
    this.icon,
    this.onPressed,
  });

  final String title;
  final bool button;
  final String? buttonName;
  final IconData? icon;
  final Function()? onPressed;
  final bool divider;

  @override
  Widget build(BuildContext context) {
    return Row(
      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          width: 4,
          height: 40,
          // padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 2),
          decoration: const BoxDecoration(
              // color: Color(0xff95170D),
              color: primaryColor,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(7), bottomLeft: Radius.circular(7))),
        ),
        Container(
          height: 40,
          width: 500,
          padding: const EdgeInsets.only(left: 8),
          decoration: BoxDecoration(
              color: themeData.scaffoldBackgroundColor,
              boxShadow: const [
                BoxShadow(
                  color: Color.fromARGB(26, 208, 208, 208),
                  // color: Colors.black,
                  blurRadius: 2,
                  offset: Offset(0, 1),
                ),
                BoxShadow(
                  color: Color.fromARGB(26, 208, 208, 208),
                  blurRadius: 10,
                  offset: Offset(0, -1),
                ),
              ]),
          // decoration: BoxDecoration(
          //   // borderRadius: const BorderRadius.only(
          //   //     topLeft: Radius.circular(7),
          //   //     bottomLeft: Radius.circular(7)),
          //   gradient: LinearGradient(
          //     stops: const [0.01, .12, .2, .9],
          //     begin: Alignment.centerLeft,
          //     end: Alignment.centerRight,
          //     colors: [
          //       themeColor.withOpacity(.1),
          //       themeColor.withOpacity(.01),
          //       themeData.scaffoldBackgroundColor,
          //       themeData.scaffoldBackgroundColor,
          //     ],
          //   ),
          // ),
          child: Row(
            children: [
              Text(
                title,
                style: const TextStyle(
                  // color: Colors.grey.shade800,

                  color: Color(0xff4f4f4f),
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  letterSpacing: 1.5,
                ),
              ),
              const SizedBox(width: 10),
              // const Spacer(),
              if (button)
                Tooltip(
                  message: buttonName,
                  child: InkWell(
                    highlightColor: Colors.transparent,
                    overlayColor:
                        const WidgetStatePropertyAll(Colors.transparent),
                    hoverColor: Colors.transparent,
                    onTap: onPressed,
                    child: Container(
                      padding: const EdgeInsets.all(3),
                      decoration: BoxDecoration(
                        // color: Color(0xffFEF2D0),
                        color: primaryColor.withOpacity(.7),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.add,
                        size: 22,
                        // color: themeColor,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              // IconButton(
              //   style: IconButton.styleFrom(
              //     backgroundColor: themeColor.withOpacity(.1),
              //     foregroundColor: themeColor,
              //     padding: EdgeInsets.zero,
              //   ),
              //   onPressed: onPressed,
              //   icon: Icon(icon, size: 20),
              // ),
              // TextButton.icon(
              //   style: TextButton.styleFrom(
              //     shadowColor: Colors.transparent,
              //     overlayColor: Colors.transparent,
              //     surfaceTintColor: Colors.transparent,
              //     padding:
              //         const EdgeInsets.symmetric(vertical: 3, horizontal: 15),
              //     backgroundColor: themeColor.withOpacity(.1),
              //     elevation: 0,
              //   ),
              //   // style: const ButtonStyle(
              //   //   elevation: WidgetStatePropertyAll(0),
              //   //   backgroundColor: WidgetStatePropertyAll(themeColor),
              //   //   foregroundColor: WidgetStatePropertyAll(Colors.white),
              //   // ),
              //   onPressed: onPressed,
              //   icon: Icon(icon, size: 18),
              //   label: Text(buttonName ?? ""),
              // ),
            ],
          ),
        ),
        /*  Spacer(),
        if (button)
          TextButton.icon(
            style: TextButton.styleFrom(
              shadowColor: Colors.transparent,
              overlayColor: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              padding: const EdgeInsets.symmetric(vertical: 3, horizontal: 15),
              backgroundColor: themeColor.withOpacity(.1),
              elevation: 0,
            ),
            // style: const ButtonStyle(
            //   elevation: WidgetStatePropertyAll(0),
            //   backgroundColor: WidgetStatePropertyAll(themeColor),
            //   foregroundColor: WidgetStatePropertyAll(Colors.white),
            // ),
            onPressed: onPressed,
            icon: Icon(icon, size: 18),
            label: Text(buttonName ?? ""),
          ), */
      ],
    );
  }
}

/* class FormHeaderTile extends StatelessWidget {
  const FormHeaderTile({
    super.key,
    required this.title,
    this.button = false,
    this.divider = true,
    this.buttonName,
    this.icon,
    this.onPressed,
  });
  final String title;
  final bool button;
  final String? buttonName;
  final IconData? icon;
  final Function()? onPressed;
  final bool divider;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Container(
            //   // width: 10,
            //   padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 2),
            //   decoration: const BoxDecoration(
            //       color: themeColor,
            //       borderRadius: BorderRadius.only(
            //           topLeft: Radius.circular(7), bottomLeft: Radius.circular(7))),
            // ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 0),
                /*  decoration: const BoxDecoration(
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(7), bottomLeft: Radius.circular(7)),
                  gradient: LinearGradient(
                    stops: const [0.01, .5],
                    begin: Alignment.centerLeft,
                    end: Alignment.centerRight,
                    colors: [
                      greenColor.withOpacity(.8),
                      greenColor.withOpacity(.5),
                    ],
                  ),
                ), */
                child: Text(
                  title,
                  style: TextStyle(
                    color: Colors.grey.shade800,
                    fontWeight: FontWeight.bold,
                    fontSize: 17.5,
                    letterSpacing: 1.5,
                  ),
                ),
              ),
            ),
            if (button)
              TextButton.icon(
                style: TextButton.styleFrom(
                  shadowColor: Colors.transparent,
                  overlayColor: Colors.transparent,
                  surfaceTintColor: Colors.transparent,
                  padding:
                      const EdgeInsets.symmetric(vertical: 3, horizontal: 15),
                  backgroundColor: themeColor.withOpacity(.1),
                  elevation: 0,
                ),
                // style: const ButtonStyle(
                //   elevation: WidgetStatePropertyAll(0),
                //   backgroundColor: WidgetStatePropertyAll(themeColor),
                //   foregroundColor: WidgetStatePropertyAll(Colors.white),
                // ),
                onPressed: onPressed,
                icon: Icon(icon, size: 18),
                label: Text(buttonName ?? ""),
              ),
          ],
        ),
        if (divider)
          const Divider(
            color: Color(0xff95170D),
            thickness: .8,
          )
      ],
    );
  }
}
 */
