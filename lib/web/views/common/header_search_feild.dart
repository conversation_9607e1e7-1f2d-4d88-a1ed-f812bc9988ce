import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../shared/theme.dart';

class SearchField extends StatelessWidget {
  const SearchField({
    super.key,
    required this.searchController,
    this.onChanged,
  });

  final TextEditingController searchController;
  final Function(String)? onChanged;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: const BoxConstraints(maxWidth: 400),
      child: Sized<PERSON><PERSON>(
        height: 45,
        child: TextFormField(
          controller: searchController,
          cursorHeight: 20,
          onChanged: onChanged,
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.symmetric(),
            fillColor: primaryColor.withValues(alpha: 0.1),
            filled: true,
            prefixIcon: const Icon(
              CupertinoIcons.search,
              size: 22,
              color: primaryColor,
            ),
            border: OutlineInputBorder(
                borderSide: BorderSide.none,
                borderRadius: BorderRadius.circular(7)),
            hintText: 'Search',
            hintStyle: const TextStyle(fontSize: 16),
          ),
        ),
      ),
    );
  }
}
