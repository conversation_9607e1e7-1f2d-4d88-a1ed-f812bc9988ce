import 'package:flutter/material.dart';

class DeleteAlert extends StatefulWidget {
  const DeleteAlert({super.key, required this.yesPressed});

  final Function() yesPressed;

  @override
  State<DeleteAlert> createState() => _DeleteAlertState();
}

class _DeleteAlertState extends State<DeleteAlert> {
  bool loading = false;
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      // backgroundColor: Colors.grey.shade50,
      // surfaceTintColor: Colors.grey.shade50,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(17)),
      title: const Text(
        'Delete',
        style: TextStyle(
            color: Colors.red, fontSize: 25, fontWeight: FontWeight.w500),
      ),
      content: const Text(
        'Are you sure you want to delete?',
        style: TextStyle(fontSize: 16),
      ),
      actions: [
        TextButton(
            onPressed: () async {
              try {
                setState(() {
                  loading = true;
                });
                await widget.yesPressed();
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              } on Exception catch (e) {
                debugPrint(e.toString());
              } finally {
                setState(() {
                  loading = false;
                });
              }
            },
            child: loading
                ? const CircularProgressIndicator()
                : const Text(
                    'Yes',
                    style: TextStyle(fontSize: 16),
                  )),
        TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No')),
      ],
    );
  }
}
