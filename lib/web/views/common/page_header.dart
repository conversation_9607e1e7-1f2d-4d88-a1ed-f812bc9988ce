import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../shared/theme.dart';

class PageHeaderWithButton extends StatelessWidget {
  const PageHeaderWithButton({
    super.key,
    required this.title,
    this.icon,
    required this.onPressed,
    this.buttonName,
    this.button = false,
    this.back = false,
    this.count,
    this.onRedirect,
    this.userName,
  });

  final String title;
  final String? count;
  final IconData? icon;
  final String? buttonName;
  final Function onPressed;
  final bool button;
  final bool back;
  final Function? onRedirect;
  final String? userName;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                if (back)
                  IconButton(
                      onPressed: () {
                        context.pop();
                      },
                      icon: const Icon(
                        CupertinoIcons.arrow_left,
                        color: primaryColor,
                      )),
                if (back) const SizedBox(width: 10),
                Text(title,
                    style: const TextStyle(fontSize: 28, color: primaryColor)),
                const SizedBox(width: 10),
                if (userName != null) ...[
                  InkWell(
                    onTap: () => onRedirect!(),
                    child: Text(userName!,
                        style:
                            const TextStyle(fontSize: 28, color: primaryColor)),
                  ),
                  const SizedBox(width: 10),
                ],
                if (count != null)
                  Text(count!,
                      style:
                          const TextStyle(fontSize: 28, color: primaryColor)),
              ],
            ),
            if (button)
              ElevatedButton.icon(
                style: ElevatedButton.styleFrom(
                  backgroundColor: primaryColor,
                  elevation: 0,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4)),
                  // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                ),
                onPressed: () => onPressed(),
                icon: Icon(
                  icon,
                  size: 20,
                ),
                label: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(buttonName ?? "",
                      style: const TextStyle(fontSize: 14)),
                ),
              ),
          ],
        ),

        // Container(
        //   height: 10,
        //   width: double.maxFinite,
        //   decoration: BoxDecoration(color: Colors.grey[300]),
        // )
      ],
    );
  }
}

class PageHeaderWithTrailingAndBack extends StatelessWidget {
  const PageHeaderWithTrailingAndBack({
    super.key,
    required this.title,
    this.trailing,
    required this.showTrailing,
  });

  final String title;
  final Widget? trailing;
  final bool showTrailing;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                IconButton(
                    onPressed: () {
                      context.pop();
                    },
                    icon: const Icon(
                      CupertinoIcons.arrow_left,
                      color: primaryColor,
                    )),
                const SizedBox(width: 10),
                Text(title,
                    style: const TextStyle(fontSize: 28, color: primaryColor)),
              ],
            ),
            if (showTrailing) trailing ?? const SizedBox(),
          ],
        ),

        // Container(
        //   height: 10,
        //   width: double.maxFinite,
        //   decoration: BoxDecoration(color: Colors.grey[300]),
        // )
      ],
    );
  }
}

class PageHeaderWithTrailing extends StatelessWidget {
  const PageHeaderWithTrailing({
    super.key,
    required this.title,
    this.trailing,
    required this.showTrailing,
  });

  final String title;
  final Widget? trailing;
  final bool showTrailing;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(title,
                style: const TextStyle(fontSize: 28, color: primaryColor)),
            if (showTrailing) trailing ?? const SizedBox(),
          ],
        ),

        // Container(
        //   height: 10,
        //   width: double.maxFinite,
        //   decoration: BoxDecoration(color: Colors.grey[300]),
        // )
      ],
    );
  }
}
