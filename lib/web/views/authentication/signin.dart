import 'package:firebase_ui_auth/firebase_ui_auth.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../shared/router.dart';
import '../../shared/theme.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    final providers = [EmailAuthProvider()];
    return Scaffold(
      // backgroundColor: Colors.white,
      body: SignInScreen(
        providers: providers,
        showAuthActionSwitch: false,
        sideBuilder: (context, constraints) {
          return Container(
              height: double.maxFinite,
              color: primaryColor.withValues(alpha: 0.1),
              child: Center(
                child: Image.asset(
                  'assets/images/app_logo.png',
                  fit: BoxFit.cover,
                  width: MediaQuery.sizeOf(context).width / 4,
                ),
              ));
        },
        actions: [
          AuthStateChangeAction<SignedIn>((context, state) {
            if (state.user != null) context.go(homeRoute);
          }),
        ],
      ),
    );
  }
}
