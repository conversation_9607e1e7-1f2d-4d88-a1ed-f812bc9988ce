import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controller/home_controller.dart';
import '../../../shared/router.dart';
import 'drawer_logout.dart';
import 'drawer_tile.dart';

class DashboardDrawer extends StatelessWidget {
  const DashboardDrawer({
    super.key,
    this.isTablet = false,
    this.scafKey,
  });

  final bool isTablet;
  final GlobalKey<ScaffoldState>? scafKey;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: isTablet ? 100 : 260,
      child: GetBuilder<HomeCtrl>(
        builder: (_) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // L O G O
              const SizedBox(height: 12),
              DashHeader(isTablet: isTablet),
              const SizedBox(height: 15),

              // I T E M S
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // requirements
                      const SizedBox(height: 20),
                      DashboardTile(
                        icon: CupertinoIcons.person_3_fill,
                        textName: 'Users',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: WebRoutes.users,
                        isSelected: true,
                      ),
                      const SizedBox(height: 10),
                      DashboardTile(
                        icon: Icons.privacy_tip_sharp,
                        textName: 'Expired Docs',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: WebRoutes.policies,
                      ),
                      const SizedBox(height: 10),
                      DashboardTile(
                        icon: CupertinoIcons.car_detailed,
                        textName: 'Vehicles',
                        tab: isTablet,
                        scafKey: scafKey,
                        isSelected: true,
                        route: WebRoutes.vehicles,
                      ),
                      const SizedBox(height: 10),
                      DashboardTile(
                        icon: CupertinoIcons.news_solid,
                        textName: 'News',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: WebRoutes.news,
                      ),
                      const SizedBox(height: 10),
                      DashboardTile(
                        icon: Icons.notification_add,
                        textName: 'Notifications',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: WebRoutes.notifications,
                      ),
                      const SizedBox(height: 10),
                      DashboardTile(
                        icon: Icons.task,
                        textName: 'Field Manager',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: WebRoutes.tasks,
                      ),
                      const SizedBox(height: 10),
                      DashboardTile(
                        icon: Icons.photo_size_select_actual_outlined,
                        textName: 'Add Banner',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: WebRoutes.banner,
                      ),
                      const SizedBox(height: 10),
                      DashboardTile(
                        icon: Icons.access_time_sharp,
                        textName: 'Recent Activity',
                        tab: isTablet,
                        scafKey: scafKey,
                        route: WebRoutes.recentActivity,
                      ),
                      const SizedBox(height: 10),
                    ],
                  ),
                ),
              ),
              // logout button
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 25),
                child: DrawerLogout(
                  tab: isTablet,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}

class DashHeader extends StatelessWidget {
  const DashHeader({
    super.key,
    this.isTablet = false,
  });

  final bool isTablet;

  @override
  Widget build(BuildContext context) {
    return Container(
        width: double.maxFinite,
        height: 80,
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(15)),
        child: Image.asset(
          'assets/images/app_logo.png',
          fit: BoxFit.contain,
        ));
  }
}
