import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../shared/firebase.dart';
import '../../../shared/router.dart';

class DrawerLogout extends StatelessWidget {
  const DrawerLogout({
    super.key,
    this.tab = false,
  });

  final bool tab;

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: () => showDialog(
          context: context,
          builder: (BuildContext context) => AlertDialog(
                title: const Text('Alert'),
                content: const Text('Are you sure you want to logout?'),
                actions: [
                  TextButton(
                      onPressed: () async {
                        await FBAuth.auth.signOut();
                        if (context.mounted) context.go(WebRoutes.signin);
                      },
                      child: const Text('Yes')),
                  TextButton(
                      onPressed: () => context.pop(), child: const Text('No')),
                ],
              )),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.grey.shade300,
        elevation: 0,
        foregroundColor: Colors.black,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
      ),
      child: tab
          ? Padding(
              padding: const EdgeInsets.symmetric(vertical: 5.0),
              child: _column(),
            )
          : _row(),
    );
  }

  Row _row() {
    return const Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          CupertinoIcons.power,
          size: 18,
        ),
        Padding(
          padding: EdgeInsets.only(left: 5.0),
          child: Text('Logout'),
        ),
      ],
    );
  }

  Column _column() {
    return const Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(CupertinoIcons.power, size: 22),
        Padding(
          padding: EdgeInsets.only(top: 5.0),
          child: Text('Logout'),
        ),
      ],
    );
  }
}
