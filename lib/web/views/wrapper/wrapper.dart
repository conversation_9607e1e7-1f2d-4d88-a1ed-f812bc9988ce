import 'package:flutter/material.dart';

import '../../shared/responsive.dart';
import 'drawer/drawer.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return true
        ? _Desktop(child)
        : ResponsiveWid(
            mobile: _Mobile(child),
            tablet: _Tablet(child),
            desktop: _Desktop(child),
          );
  }
}

// D E S K T O P

class _Desktop extends StatelessWidget {
  const _Desktop(this.child);

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: const Color.fromARGB(255, 250, 250, 250),
      // backgroundColor: Color.fromARGB(255, 214, 213, 213),
      body: Row(
        children: [
          Container(
            decoration:
                const BoxDecoration(color: Color.fromARGB(255, 255, 244, 228)),
            padding: const EdgeInsets.all(8.0),
            child: const DashboardDrawer(),
          ),
          // const SizedBox(width: 20),
          Expanded(child: child),
        ],
      ),
    );
  }
}

// T A B L E T

class _Tablet extends StatelessWidget {
  const _Tablet(this.child);

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // backgroundColor: const Color.fromARGB(255, 250, 250, 250),
      body: Row(
        children: [
          // side bar
          Container(
            color: Colors.white,
            padding: const EdgeInsets.all(8.0),
            child: const DashboardDrawer(isTablet: true),
          ),
          // const SizedBox(width: 10),

          // const SizedBox(width: 20),
          // home page
          Expanded(child: child),
        ],
      ),
    );
  }
}

// M O B I L E

final _scafKey = GlobalKey<ScaffoldState>();

class _Mobile extends StatelessWidget {
  const _Mobile(this.child);

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      key: _scafKey,
      appBar: AppBar(
        title: const Text('mass_ibs_'),
        centerTitle: true,
        scrolledUnderElevation: 12,
        elevation: 12,
        surfaceTintColor: Colors.white,
      ),
      drawer: Theme(
        data: ThemeData(
            canvasColor: Colors.white,
            drawerTheme: const DrawerThemeData(
              backgroundColor: Colors.white,
              surfaceTintColor: Colors.white,
            )),
        child: Drawer(
            child: Padding(
          padding: const EdgeInsets.all(8),
          child: DashboardDrawer(scafKey: _scafKey),
        )),
      ),
      body: child,
    );
  }
}
