import 'package:flutter/material.dart';

class ColorConstants {
  ColorConstants._();

  // Light
  static Color lightScaffoldBackgroundColor = Color(0xFFFFFFFF);
  static Color secondaryAppColor = Color(0xFF4354A4);

  // Dark
  static Color darkScaffoldBackgroundColor = Color(0xFF2F2E2E);
  static Color secondaryDarkAppColor = Colors.white;

  // Other
  static Color textFieldHintColor = Color(0xFF828282);
  static Color borderColor = Color(0xFFE0E0E0);
  static Color grayBackGroundColor = Color(0xFFEFEFEF);
  static Color redColor = Colors.red;
  static Color tipColor = Color(0xFFB6B6B6);
  static Color lightGray = Color(0xFFACACAC);
  static Color darkGray = Color(0xFF9F9F9F);
  static Color black = Color(0xFF000000);
  static Color white = Color(0xFFFFFFFF);
}

class AppInsets {
  AppInsets._();

  static const double s1 = 1;
  static const double s2 = 2;
  static const double s3 = 3;
  static const double s4 = 4;
  static const double s5 = 5;
  static const double s6 = 6;
  static const double s7 = 7;
  static const double s8 = 8;
  static const double s10 = 10;
  static const double s12 = 12;
  static const double s14 = 14;
  static const double s16 = 16;
  static const double s18 = 18;
  static const double s20 = 20;
  static const double s22 = 22;
  static const double s24 = 24;
  static const double s28 = 28;
  static const double s30 = 30;
  static const double s32 = 32;
  static const double s36 = 36;
  static const double s38 = 38;
  static const double s40 = 40;
  static const double s44 = 44;
  static const double s48 = 48;
  static const double s56 = 56;
  static const double s64 = 64;
  static const double s70 = 70;
  static const double s72 = 72;
  static const double s76 = 76;
  static const double s80 = 80;
  static const double s86 = 86;
  static const double s88 = 88;
  static const double s92 = 92;
  static const double s96 = 96;
  static const double s100 = 100;
  static const double s104 = 104;
  static const double s108 = 108;
  static const double s110 = 110;
  static const double s112 = 112;
  static const double s116 = 116;

  static const double pageMargin = 16;
}

class AppFonts {
  AppFonts._();

  static const String lexend = "Lexend";
}

Color hexToColor(String hex) {
  assert(RegExp(r'^#([0-9a-fA-F]{6})|([0-9a-fA-F]{8})$').hasMatch(hex),
      'hex color must be #rrggbb or #rrggbbaa');

  return Color(
    int.parse(hex.substring(1), radix: 16) +
        (hex.length == 7 ? 0xff000000 : 0x00000000),
  );
}
