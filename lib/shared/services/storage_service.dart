import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/shared/constants/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../web/models/user_model.dart';

class StorageService extends GetxService {
  Future<SharedPreferences> init() async {
    return await SharedPreferences.getInstance();
  }

// Fetch user from SharedPreferences
  static Future<UserModel?> getUserFromPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = prefs.getString(StorageConstants.userInfo);

    if (userJson != null) {
      final Map<String, dynamic> data = jsonDecode(userJson);

      // Convert 'createdAt' back to Timestamp
      data['createdAt'] = Timestamp.fromDate(DateTime.parse(data['createdAt']));

      return UserModel.fromJson(data);
    }
    return null;
  }

  static logout() async {
    final prefs = await SharedPreferences.getInstance();
    prefs.clear();
  }
}
