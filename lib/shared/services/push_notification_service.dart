import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:mass_ibs/shared/firebase.dart';

class PushNotificationService {
  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  Future<void> initialize(String userId) async {
    // Request permission on iOS
    await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        announcement: true);

    // Get FCM token
    String? token = await _firebaseMessaging.getToken();

    if (token != null) {
      debugPrint("Fire Token: $token");
      await FBFireStore.users.doc(userId).update({
        'firebaseToken': token,
      });
    }

    // Android channel for foreground notifications
    const AndroidInitializationSettings androidInitSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initSettings =
        InitializationSettings(android: androidInitSettings);

    await _flutterLocalNotificationsPlugin.initialize(initSettings);

    // Listen to foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      RemoteNotification? notification = message.notification;
      AndroidNotification? android = message.notification?.android;

      if (notification != null && android != null) {
        _flutterLocalNotificationsPlugin.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              'massIBS',
              'Mass Notification Channel',
              importance: Importance.high,
              priority: Priority.high,
            ),
          ),
        );
      }
    });

    // Handle when app is opened from background or terminated
    FirebaseMessaging.onMessageOpenedApp.listen((message) {
      print("App opened via notification");
    });
  }

  Future<void> subscribeToTopics(List<String> topics) async {
    for (String topic in topics) {
      await _firebaseMessaging.subscribeToTopic(topic);
    }
  }
}
