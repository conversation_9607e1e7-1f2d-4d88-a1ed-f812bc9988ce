import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fb = FirebaseFirestore.instance;
  static final users = fb.collection('users');
  static final tasks = fb.collection('tasks');
  static final banners = fb.collection('banners');
  static final vehicles = fb.collection('vehicles');
  static final usersDocs = fb.collection('usersDocs');
  static final myDocuments = fb.collection('PersonalDocuments');
  static final news = fb.collection('news');
  static final notifications = fb.collection('notifies');
  static final recents = fb.collection('recents');
  static final setting = fb.collection('setting').doc('setsData');
}

class FBStorage {
  static final fbStore = FirebaseStorage.instance;
  static final banners = fbStore.ref().child('Banners');
  static final vehicleDocs = fbStore.ref().child('VehicleDocs');
  static final userDocs = fbStore.ref().child('UserDocs');
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}
