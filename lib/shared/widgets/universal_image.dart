import 'dart:io';

import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../shared/widgets/circular_progress.dart';
import '../../shared/widgets/flutter_svg_provider.dart' as svg_provider;

class UniversalImage extends StatelessWidget {
  const UniversalImage(
    this.uri, {
    super.key,
    this.color,
    this.width,
    this.height,
    this.fit,
    this.isCircle = false,
    this.showProgress = false,
    this.cacheHeight,
    this.cacheWidth,
  });

  final String uri;
  final Color? color;
  final double? width;
  final double? height;
  final int? cacheWidth;
  final int? cacheHeight;
  final BoxFit? fit;
  final bool isCircle;
  final bool showProgress;

  @override
  Widget build(BuildContext context) {
    // don't remove devicePixelRatio because its require when we use cache size of image
    double devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    if (uri.isEmpty) {
      return const Center(
        child: SizedBox(
            // height: AppInsets.s30,
            // width: AppInsets.s30,
            // child: CircularProgress(),
            ),
      );
    }

    if (!isCircle) {
      if (uri.startsWith('assets')) {
        if (uri.endsWith('.svg')) {
          return SvgPicture.asset(
            uri,
            colorFilter: color != null
                ? ColorFilter.mode(
                    color!,
                    BlendMode.srcIn,
                  )
                : null,
            width: width,
            height: height,
            fit: fit ?? BoxFit.contain,
            placeholderBuilder: showProgress
                ? (context) {
                    return const CircularProgress();
                  }
                : null,
          );
        } else {
          return ExtendedImage.asset(
            uri,
            color: color,
            width: width,
            height: height,
            fit: fit,
          );
        }
      } else if (uri.startsWith('/')) {
        return Image.file(
          File(uri),
          color: color,
          width: width,
          height: height,
          cacheHeight: cacheHeight,
          cacheWidth: cacheWidth ??
              (width != null
                  ? (width!.toInt() * devicePixelRatio).round()
                  : null),
          fit: fit,
        );
      } else {
        return ExtendedImage.network(
          uri,
          color: color,
          width: width,
          height: height,
          fit: fit,
          cache: true,
          retries: 3,
          loadStateChanged: (ExtendedImageState state) {
            switch (state.extendedImageLoadState) {
              case LoadState.loading:
                return const Center(child: CircularProgress());
              case LoadState.completed:
                return null;
              case LoadState.failed:
                state.reLoadImage();
                return const SizedBox();
            }
          },
        );
      }
    } else {
      late final ImageProvider imageProvider;
      if (uri.startsWith('assets')) {
        if (uri.endsWith('.svg')) {
          imageProvider = svg_provider.Svg(uri);
        } else {
          imageProvider = AssetImage(uri);
        }
      } else if (uri.startsWith('/')) {
        imageProvider = FileImage(File(uri));
      } else {
        imageProvider = ExtendedNetworkImageProvider(
          uri,
          cacheRawData: true,
        );
      }

      final double radius =
          width != null ? width! / 2 : (height != null ? height! / 2 : 16);
      return CircleAvatar(
        backgroundImage: imageProvider,
        backgroundColor: Colors.transparent,
        onBackgroundImageError: (Object exception, StackTrace? stackTrace) {
          // Do nothing here and catch error
        },
        radius: radius,
      );
    }
  }

// Widget _error() {
//   if (!isCircle) {
//     return Container(
//       padding: const EdgeInsets.all(AppInsets.s16),
//       decoration: BoxDecoration(
//         color: AppColors.bgColor.withOpacity(0.2),
//         shape: BoxShape.rectangle,
//       ),
//       width: width,
//       height: height,
//       child: const Center(
//           child: Icon(
//         Icons.error,
//         size: 24,
//       )),
//     );
//   } else {
//     final double radius =
//         width != null ? width! / 2 : (height != null ? height! / 2 : 16);
//     return CircleAvatar(
//       radius: radius,
//       backgroundColor: AppColors.bgColor,
//     );
//   }
// }
}
