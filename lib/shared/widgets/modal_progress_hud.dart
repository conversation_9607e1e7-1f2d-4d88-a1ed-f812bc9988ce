import 'package:flutter/material.dart';

import '../constants/colors.dart';
import 'circular_progress.dart';

class ModalProgressHUD extends StatelessWidget {
  const ModalProgressHUD({
    super.key,
    required this.inAsyncCall,
    this.opacity = 0.3,
    this.color,
    this.progressIndicator = const CircularProgress(),
    this.dismissible = false,
    required this.child,
  });

  final bool inAsyncCall;
  final double opacity;
  final Color? color;
  final Widget progressIndicator;
  final bool dismissible;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        child,
        if (inAsyncCall) ...[
          Positioned(
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
            child: Opacity(
              opacity: opacity,
              child: ModalBarrier(
                dismissible: dismissible,
                color: color ?? ColorConstants.secondaryAppColor,
              ),
            ),
          ),
          Positioned(
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
            child: Center(child: progressIndicator),
          )
        ]
      ],
    );
  }
}
