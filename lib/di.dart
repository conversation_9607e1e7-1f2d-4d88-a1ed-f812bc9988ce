import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/modules/home/<USER>';
import 'package:shared_preferences/shared_preferences.dart';

import 'modules/auth/auth_controller.dart';
import 'modules/splash/splash_controller.dart';
import 'shared/services/services.dart';

class DependencyInjection {
  static Future<void> init() async {
    await Firebase.initializeApp();
    await FirebaseAppCheck.instance.activate(
      androidProvider: AndroidProvider.playIntegrity,
    );
    final prefs = await SharedPreferences.getInstance();
    Get.put<SharedPreferences>(prefs);
    // Synchronous injections
    Get.put(SplashController());
    Get.put(AuthController());
    Get.put(HomeController());
    // Asynchronous injection
    await Get.putAsync(() => StorageService().init());
  }
}
