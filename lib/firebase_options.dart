// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAm5wknvXS-1ovb-TVloGhC5lZnXAUnExA',
    appId: '1:966871248548:web:c5e1e372a8c79f7f380031',
    messagingSenderId: '966871248548',
    projectId: 'massibs',
    authDomain: 'massibs.firebaseapp.com',
    storageBucket: 'massibs.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBvD9swPXy6xpNvW0yFBg2u_XBDXcq4NVg',
    appId: '1:966871248548:android:027a75df797c83aa380031',
    messagingSenderId: '966871248548',
    projectId: 'massibs',
    storageBucket: 'massibs.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBnjOJhHxwoGXW34OWIH21-n31ekcLSOs0',
    appId: '1:966871248548:ios:586a4ed43df60b21380031',
    messagingSenderId: '966871248548',
    projectId: 'massibs',
    storageBucket: 'massibs.firebasestorage.app',
    iosBundleId: 'com.diwizon.massIbs',
  );

}