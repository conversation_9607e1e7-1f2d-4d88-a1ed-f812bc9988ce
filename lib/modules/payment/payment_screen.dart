import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/web/shared/theme.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../shared/constants/colors.dart';
import '../../shared/constants/image_constants.dart';
import '../../shared/widgets/universal_image.dart';

class PaymentScreen extends StatelessWidget {
  const PaymentScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        centerTitle: true,
        backgroundColor: Colors.white,
        leading: InkWell(
          onTap: Get.back,
          child: Icon(
            Icons.arrow_back,
            color: ColorConstants.black,
            size: AppInsets.s24,
          ),
        ),
        title: Text(
          "Payment",
          style: TextStyle(
            fontSize: AppInsets.s18,
            color: ColorConstants.black,
            fontWeight: FontWeight.w600,
            fontFamily: AppFonts.lexend,
          ),
        ),
      ),
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            Divider(
              color: ColorConstants.grayBackGroundColor,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Card(
                    elevation: 3,
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16)),
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        children: [
                          UniversalImage(
                            ImageConstants.yesBank,
                            width: MediaQuery.sizeOf(context).width / 2,
                          ),
                          const SizedBox(height: 24),
                          QrImageView(
                            data: "upi://pay?pa=yespay.bizsbiz9833@yesbankltd",
                            version: QrVersions.auto,
                            size: MediaQuery.sizeOf(context).width / 2,
                          ),
                          const SizedBox(height: 24),
                          _infoTile("UPI ID", "yespay.bizsbiz9833@yesbankltd",
                              Icons.payment_rounded),
                          _infoTile("Number", "+91 **********", Icons.phone),
                          _infoTile("Account Number", "***************",
                              Icons.account_balance),
                          _infoTile("IFSC Code", "YESB0000009", Icons.comment_bank),
                          _infoTile(
                              "Address",
                              "Third floor \nOffice no 5,6, \nVrund complex, \nAkota, Vadodara, \nGujarat, 390020",
                              Icons.home),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _infoTile(
    String title,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: primaryColor,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            maxLines: 5,
            style: const TextStyle(
              fontSize: 14,
              // fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              maxLines: 5,
              textAlign: TextAlign.right,
              style: const TextStyle(
                color: Colors.black87,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
