import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_update/in_app_update.dart';

import '../../shared/services/storage_service.dart';
import '../../web/models/user_model.dart';
import '../auth/login_screen.dart';
import '../home/<USER>';

class SplashController extends GetxController {
  @override
  void onInit() {
    checkForForceUpdate();
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();

    await Future.delayed(Duration(milliseconds: 2000));

    try {
      UserModel? user = await StorageService.getUserFromPrefs();

      if (user != null) {
        // User exists, navigate to HomePage
        Get.offAll(() => HomeScreen());
      } else {
        // User doesn't exist, navigate to LoginPage
        Get.offAll(() => LoginScreen());
      }
    } catch (e) {
      Get.offAll(() => LoginScreen());
    }
  }

  Future<void> checkForForceUpdate() async {
    try {
      final updateInfo = await InAppUpdate.checkForUpdate();

      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable &&
          updateInfo.immediateUpdateAllowed) {
        await InAppUpdate.performImmediateUpdate();
      }
    } catch (e) {
      debugPrint("Error checking for update: $e");
    }
  }
}
