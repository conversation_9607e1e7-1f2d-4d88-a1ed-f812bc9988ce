import 'package:flutter/material.dart';
import 'package:mass_ibs/shared/shared.dart';
import 'package:mass_ibs/shared/widgets/universal_image.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);
    return Container(
      color: ColorConstants.white,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: UniversalImage(
            ImageConstants.splashLogo,
          ),
        ),
      ),
    );
  }
}
