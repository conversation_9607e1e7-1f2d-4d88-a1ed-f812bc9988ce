import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/modules/add_vehicle/add_vehicle.dart';
import 'package:mass_ibs/shared/shared.dart';

class AddVehicleScreen extends GetView<AddVehicleController> {
  AddVehicleScreen({super.key});

  var notificationCount = 1;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          centerTitle: true,
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: Get.back,
            child: Icon(
              Icons.arrow_back,
              color: ColorConstants.black,
              size: AppInsets.s24,
            ),
          ),
          title: Text(
            "Add Vehicle",
            style: TextStyle(
              fontSize: AppInsets.s18,
              color: ColorConstants.black,
              fontWeight: FontWeight.w600,
              fontFamily: AppFonts.lexend,
            ),
          ),
        ),
        body: SafeArea(
          child: Column(
            children: [
              Divider(
                color: ColorConstants.grayBackGroundColor,
              ),
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: AppInsets.pageMargin,
                  vertical: AppInsets.s24,
                ),
                child: _buildView(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _buildView() {
    return Column(
      spacing: AppInsets.s24,
      children: [
        TextField(
          controller: controller.vehicleNumberController,
          textCapitalization: TextCapitalization.characters,
          onChanged: (value) {},
          style: TextStyle(
            fontSize: AppInsets.s18,
            fontWeight: FontWeight.w600,
            fontFamily: AppFonts.lexend,
            color: ColorConstants.black,
          ),
          // inputFormatters: [
          //   FilteringTextInputFormatter.digitsOnly,
          //   LengthLimitingTextInputFormatter(6),
          // ],
          decoration: InputDecoration(
            labelText: 'Vehicle Number',
            // errorText: controller.errorMessageOTP.value.isEmpty
            //     ? null
            //     : controller.errorMessageOTP.value,
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(
                color: ColorConstants.redColor,
              ), // Default border
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(
                color: ColorConstants.redColor,
              ), // Default border
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(color: ColorConstants.textFieldHintColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(color: ColorConstants.textFieldHintColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(
                color: ColorConstants.secondaryAppColor,
                width: 2.0,
              ),
            ),
            filled: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            // Inner padding
            hintText: "Enter vehicle number",
            hintStyle: TextStyle(
              fontSize: AppInsets.s16,
              color: ColorConstants.textFieldHintColor,
              fontWeight: FontWeight.w600,
              fontFamily: AppFonts.lexend,
            ),
          ),
        ),
        TextField(
          controller: controller.chassisNumberController,
          textCapitalization: TextCapitalization.characters,
          keyboardType: TextInputType.text,
          onChanged: (value) {},
          style: TextStyle(
            fontSize: AppInsets.s18,
            fontWeight: FontWeight.w600,
            fontFamily: AppFonts.lexend,
            color: ColorConstants.black,
          ),
          inputFormatters: [
            LengthLimitingTextInputFormatter(5),
          ],
          decoration: InputDecoration(
            labelText: 'Chassis No. (Last 5 Digits)*',
            // errorText: controller.errorMessageOTP.value.isEmpty
            //     ? null
            //     : controller.errorMessageOTP.value,
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(
                color: ColorConstants.redColor,
              ), // Default border
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(
                color: ColorConstants.redColor,
              ), // Default border
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(color: ColorConstants.textFieldHintColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(color: ColorConstants.textFieldHintColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(
                color: ColorConstants.secondaryAppColor,
                width: 2.0,
              ),
            ),
            filled: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
            // Inner padding
            hintText: "Enter chassis number",
            hintStyle: TextStyle(
              fontSize: AppInsets.s16,
              color: ColorConstants.textFieldHintColor,
              fontWeight: FontWeight.w600,
              fontFamily: AppFonts.lexend,
            ),
          ),
        ),
        DropdownMenu<VehicleType>(
          enableSearch: false,
          requestFocusOnTap: false,
          controller: controller.vehicleTypeController,
          width: double.infinity,
          label: const Text('Vehicle Type'),
          textStyle: TextStyle(
            fontSize: AppInsets.s16,
            color: ColorConstants.black,
            fontWeight: FontWeight.w600,
            fontFamily: AppFonts.lexend,
          ),
          inputDecorationTheme: InputDecorationTheme(
            isDense: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(
                color: ColorConstants.redColor,
              ), // Default border
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(
                color: ColorConstants.redColor,
              ), // Default border
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(
                color: ColorConstants.redColor,
              ), // Default border
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(color: ColorConstants.textFieldHintColor),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(color: ColorConstants.textFieldHintColor),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppInsets.s4),
              borderSide: BorderSide(color: ColorConstants.textFieldHintColor),
            ),
          ),
          onSelected: (VehicleType? type) {
            controller.updateVehicleType(type!);
          },
          dropdownMenuEntries: VehicleType.entries,
        ),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.secondaryAppColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppInsets.s4),
              ),
              padding: EdgeInsets.symmetric(vertical: AppInsets.s16),
              elevation: 0,
            ),
            onPressed: () {},
            child: Text(
              'Add Vehicle',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: AppInsets.s16,
                fontWeight: FontWeight.w600,
                fontFamily: AppFonts.lexend,
              ),
            ),
          ),
        )
      ],
    );
  }
}
