import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../shared/constants/colors.dart';

class AddVehicleController extends GetxController {
  AddVehicleController();

  final vehicleNumberController = TextEditingController();
  final chassisNumberController = TextEditingController();
  final vehicleTypeController = TextEditingController();
  final selectedVehicleType = Rx<VehicleType?>(null);

  void updateVehicleType(VehicleType type) {
    selectedVehicleType.value = type;
  }
}

enum VehicleType {
  mcwog('Motorcycle without Gear (MCWOG)'),
  mcwg('Motorcycle with Gear (MCWG)'),
  lmv('Light Motor Vehicles (LMV)'),
  hmv('Heavy Motor Vehicles (HMV)'),
  other('Other');

  const VehicleType(this.label);

  final String label;

  static final List<DropdownMenuEntry<VehicleType>> entries =
      UnmodifiableListView(
    values.map(
      (VehicleType type) => DropdownMenuEntry(
        value: type,
        label: type.label,
        style: MenuItemButton.styleFrom(
          foregroundColor: ColorConstants.black,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          textStyle: TextStyle(
            fontSize: AppInsets.s16,
            color: ColorConstants.black,
            fontWeight: FontWeight.w600,
            fontFamily: AppFonts.lexend,
          ),
        ),
      ),
    ),
  );
}
