import 'dart:async';
import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/shared/shared.dart';
import 'package:mass_ibs/web/models/user_docs_model.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import '../../shared/widgets/universal_image.dart';
import '../../web/shared/theme.dart';
import '../home/<USER>';

class PreviewScreen extends StatefulWidget {
  const PreviewScreen(
      {super.key,
      required this.docName,

      // required this.fileUrl,
      // required this.fileType,
      required this.files});

  final String docName;
  // final String? fileUrl;
  // final String fileType;
  final List<DocFiles> files;

  @override
  State<PreviewScreen> createState() => _PreviewScreenState();
}

class _PreviewScreenState extends State<PreviewScreen> {
  final _homeController = Get.find<HomeController>();
  bool isSharing = false;

  List<String> remotePDFPath = [];
  int? pages = 0;
  int? currentPage = 0;
  bool isReady = false;
  String errorMessage = '';
  final Completer<PDFViewController> _controller =
      Completer<PDFViewController>();

  @override
  void initState() {
    super.initState();
    for (var i = 0; i < widget.files.length; i++) {
      createFileOfPdfUrl(i).then((f) {
        if (mounted) {
          setState(() {
            remotePDFPath.add(f.path);
          });
        }
      });
    }
  }

  Future<File> createFileOfPdfUrl(int index) async {
    Completer<File> completer = Completer();
    debugPrint("Start download file from internet!");
    try {
      final url = widget.files[index].fileUrl;
      final filename = url.substring(url.lastIndexOf("/") + 1);
      var request = await HttpClient().getUrl(Uri.parse(url));
      var response = await request.close();
      var bytes = await consolidateHttpClientResponseBytes(response);
      var dir = await getApplicationDocumentsDirectory();
      debugPrint("Download files");
      debugPrint("${dir.path}/$filename");
      File file = File("${dir.path}/$filename");

      await file.writeAsBytes(bytes, flush: true);
      completer.complete(file);
    } catch (e) {
      throw Exception('Error parsing asset file!');
    }

    return completer.future;
  }

  final ctrl = CarouselSliderController();
  int ind = 0;
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
          appBar: AppBar(
            automaticallyImplyLeading: false,
            titleSpacing: 0,
            centerTitle: true,
            backgroundColor: Colors.white,
            leading: InkWell(
              onTap: Get.back,
              child: Icon(
                Icons.arrow_back,
                color: ColorConstants.black,
                size: AppInsets.s24,
              ),
            ),
            actions: [
              InkWell(
                onTap: () =>
                    _homeController.redirectUrl(widget.files[ind].fileUrl),
                child: Icon(
                  CupertinoIcons.cloud_download,
                  color: ColorConstants.black,
                  size: AppInsets.s24,
                ),
              ),
              const SizedBox(width: 8),
            ],
            title: Text(
              widget.docName.toUpperCase(),
              style: TextStyle(
                fontSize: AppInsets.s18,
                color: ColorConstants.black,
                fontWeight: FontWeight.w600,
                fontFamily: AppFonts.lexend,
              ),
            ),
          ),
          body: SafeArea(
              child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: AppInsets.s24,
              vertical: AppInsets.s24,
            ),
            child: CarouselSlider(
              carouselController: ctrl,
              options: CarouselOptions(
                enableInfiniteScroll: false,
                viewportFraction: 1,
                height: SizeConfig().screenHeight / 1.5,
                onPageChanged: (index, reason) {
                  ind = index;
                },
              ),
              items: List.generate(
                widget.files.length,
                (index) {
                  return Column(
                    children: [
                      if (widget.files[index].fileType == 'image')
                        Container(
                          width: double.infinity,
                          // padding: const EdgeInsets.all(AppInsets.s6),
                          decoration: BoxDecoration(
                            color: ColorConstants.grayBackGroundColor,
                            borderRadius: BorderRadius.circular(AppInsets.s16),
                          ),
                          child: UniversalImage(
                            widget.files[index].fileUrl != ""
                                ? widget.files[index].fileUrl
                                : SvgsConstants.document,
                            fit: BoxFit.cover,
                            width: SizeConfig().screenWidth / 3,
                            height: SizeConfig().screenHeight / 2,
                          ),
                        )
                      else
                        Expanded(
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(AppInsets.s6),
                            decoration: BoxDecoration(
                              color: ColorConstants.grayBackGroundColor,
                              borderRadius:
                                  BorderRadius.circular(AppInsets.s16),
                            ),
                            child: remotePDFPath.isNotEmpty
                                ? PDFView(
                                    filePath: remotePDFPath[index],
                                    enableSwipe: true,
                                    swipeHorizontal: true,
                                    autoSpacing: false,
                                    pageFling: true,
                                    pageSnap: true,
                                    defaultPage: currentPage ?? 0,
                                    fitPolicy: FitPolicy.BOTH,
                                    preventLinkNavigation: false,
                                    backgroundColor: Color(0xFFFEF7FF),
                                    onRender: (pages) {
                                      setState(() {
                                        pages = pages;
                                        isReady = true;
                                      });
                                    },
                                    onError: (error) {
                                      setState(() {
                                        errorMessage = error.toString();
                                      });
                                      debugPrint(error.toString());
                                    },
                                    onPageError: (page, error) {
                                      setState(() {
                                        errorMessage =
                                            '$page: ${error.toString()}';
                                      });
                                      debugPrint('$page: ${error.toString()}');
                                    },
                                    onViewCreated:
                                        (PDFViewController pdfViewController) {
                                      _controller.complete(pdfViewController);
                                    },
                                    onLinkHandler: (String? uri) {
                                      debugPrint('goto uri: $uri');
                                    },
                                    onPageChanged: (int? page, int? total) {
                                      debugPrint(
                                          'page change: ${page ?? 0 + 1}/$total');
                                      setState(() {
                                        currentPage = page;
                                      });
                                    },
                                  )
                                : Center(
                                    child: const CircularProgressIndicator()),
                          ),
                        ),
                      const SizedBox(height: AppInsets.s32),
                      if (isSharing)
                        Padding(
                          padding: const EdgeInsets.only(right: 12),
                          child: CircularProgressIndicator(
                            color: primaryColor,
                          ),
                        )
                      else
                        SizedBox(
                          width: SizeConfig().screenWidth / 3,
                          child: ElevatedButton.icon(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: ColorConstants.secondaryAppColor,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(AppInsets.s4),
                              ),
                              padding:
                                  EdgeInsets.symmetric(vertical: AppInsets.s16),
                              elevation: 0,
                            ),
                            onPressed: (!isReady &&
                                    widget.files[index].fileType
                                            .toLowerCase() ==
                                        "pdf")
                                ? () {}
                                : () async {
                                    setState(() => isSharing = true);
                                    if (widget.files[index].fileType
                                                .toLowerCase() ==
                                            'pdf' &&
                                        remotePDFPath.isNotEmpty) {
                                      final uri = Uri.parse(
                                          widget.files[index].fileUrl);
                                      final response =
                                          await HttpClient().getUrl(uri);
                                      final imageBytes =
                                          await consolidateHttpClientResponseBytes(
                                              await response.close());
                                      // final file = XFile(remotePDFPath);
                                      await SharePlus.instance.share(
                                        ShareParams(
                                          title: widget.docName,
                                          files: [
                                            XFile.fromData(imageBytes,
                                                mimeType: 'application/pdf')
                                          ],
                                        ),
                                      );
                                    } else if (widget.files[index].fileType
                                                .toLowerCase() ==
                                            'image' &&
                                        widget.files[index].fileUrl != '') {
                                      print("entered");
                                      try {
                                        final uri = Uri.parse(
                                            widget.files[index].fileUrl);
                                        final response =
                                            await HttpClient().getUrl(uri);
                                        final imageBytes =
                                            await consolidateHttpClientResponseBytes(
                                                await response.close());
                                        // final dir = await getTemporaryDirectory();
                                        // final imagePath =
                                        //     '${dir.path}/${widget.docName}.jpg';
                                        // final file =
                                        //     await File(imagePath).writeAsBytes(imageBytes);

                                        await SharePlus.instance.share(
                                          ShareParams(
                                            title: widget.docName,
                                            files: [
                                              XFile.fromData(imageBytes,
                                                  mimeType: 'image/jpeg')
                                              // 'image/${widget.files[index].fileUrl.split('.').last.split("?").first}')
                                            ],
                                          ),
                                        );
                                      } catch (e) {
                                        debugPrint("❌ Share error: $e");
                                        if (widget.files[index].fileUrl != '') {
                                          await SharePlus.instance.share(
                                            ShareParams(
                                              text: widget.files[index]
                                                          .fileUrl !=
                                                      ''
                                                  ? null
                                                  : widget.files[index].fileUrl,
                                              subject: widget.docName,
                                            ),
                                          );
                                        } else {
                                          Get.snackbar("Error",
                                              "Unable to share this document.");
                                        }
                                      } finally {
                                        setState(() => isSharing = false);
                                      }
                                    }
                                  },
                            icon: Icon(
                              Icons.share,
                              color: ColorConstants.white,
                              size: AppInsets.s24,
                            ),
                            label: Text(
                              'Share',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: AppInsets.s16,
                                fontWeight: FontWeight.w600,
                                fontFamily: AppFonts.lexend,
                              ),
                            ),
                            // child:,
                          ),
                        )
                    ],
                  );
                },
              ),
            ),
          ))),
    );
  }
}
