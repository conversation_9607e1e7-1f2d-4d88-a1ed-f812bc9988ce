import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mass_ibs/modules/home/<USER>';
import 'package:mass_ibs/shared/shared.dart';
import 'package:mass_ibs/web/models/user_docs_model.dart';
import 'package:mass_ibs/web/models/vehicle_model.dart';
import 'package:mass_ibs/web/shared/theme.dart';
import 'package:uuid/uuid.dart';

import '../../shared/firebase.dart';
import '../../shared/widgets/universal_image.dart';
import '../../web/models/task_model.dart';
import '../../web/models/user_model.dart';
import '../../web/services/image_picker.dart';
import '../../web/shared/methods.dart';
import '../preview_doc/preview_screen.dart';

class VehicleScreen extends StatefulWidget {
  const VehicleScreen({
    super.key,
    required this.vehicleModel,
  });

  final VehicleModel vehicleModel;

  @override
  State<VehicleScreen> createState() => _VehicleScreenState();
}

class _VehicleScreenState extends State<VehicleScreen> {
  final _homeController = Get.find<HomeController>();

  String? selectedTaskName;
  List<String> taskNames = [];
  List<TaskModel> taskModels = [];

  @override
  void initState() {
    super.initState();
    _loadTaskModels();
  }

  Future<void> _loadTaskModels() async {
    try {
      final taskCollection = await FBFireStore.tasks.get();
      List<TaskModel> fetchedTaskModels = [];

      for (var doc in taskCollection.docs) {
        final taskModel = TaskModel.fromSnap(doc); // Convert to TaskModel

        // Handle empty taskDetails safely
        List<TaskDetailModel> taskDetails = [];
        if (doc.data()['fields'] != null) {
          taskDetails = (doc.data()['fields'] as List<dynamic>)
              .map((e) => TaskDetailModel.fromJson(
                  e['title'], e as Map<String, dynamic>))
              .toList();
        }

        // Directly assigning the updated task details
        taskModel.taskDetails = taskDetails;

        fetchedTaskModels.add(taskModel); // Add to the list
      }

      setState(() {
        taskModels = fetchedTaskModels;
        selectedTaskName =
            taskModels.isNotEmpty ? taskModels.first.taskName : null;
      });
    } catch (e) {
      debugPrint("Error loading task models: $e");
    }
    debugPrint("Task models: $taskModels");
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          centerTitle: true,
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: Get.back,
            child: Icon(
              Icons.arrow_back,
              color: ColorConstants.black,
              size: AppInsets.s24,
            ),
          ),
          title: Text(
            "${widget.vehicleModel.vehicleName.toUpperCase()} - ${widget.vehicleModel.vehicleNumber.toUpperCase()}",
            style: TextStyle(
              fontSize: AppInsets.s18,
              color: ColorConstants.black,
              fontWeight: FontWeight.w600,
              fontFamily: AppFonts.lexend,
            ),
          ),
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () {
            // TODO: Upload Document
            showTaskSelectionDialog(context);
          },
          icon: Icon(Icons.file_upload_outlined, size: AppInsets.s24),
          label: Text(
            'Upload documents',
            style: TextStyle(
              fontSize: AppInsets.s16,
              fontWeight: FontWeight.w600,
              fontFamily: AppFonts.lexend,
            ),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppInsets.s4),
          ),
          extendedPadding: EdgeInsets.symmetric(
            horizontal: AppInsets.s16,
            // vertical: AppInsets.s4,
          ),
        ),
        body: SafeArea(
          child: Column(
            children: [
              Divider(
                color: ColorConstants.grayBackGroundColor,
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppInsets.pageMargin,
                    vertical: AppInsets.s8,
                  ),
                  child: _myDocumentList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _myDocumentList() {
    return StreamBuilder(
        stream: FBFireStore.usersDocs
            .where('vehicleDoc', isEqualTo: true)
            .where('vehicleId', isEqualTo: widget.vehicleModel.docId)
            .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return Text("Facing some issue!!");
          } else if (snapshot.hasData) {
            List<UserDocsModel> docs = snapshot.data?.docs
                    .map((e) => UserDocsModel.fromSnapshot(e))
                    .toList() ??
                [];
            return ListView.separated(
              shrinkWrap: true,
              padding: const EdgeInsets.symmetric(vertical: AppInsets.s16),
              itemCount: docs.length,
              itemBuilder: (context, index) {
                UserDocsModel vehicleDocument = docs[index];
                return GestureDetector(
                  onTap: () => vehicleDocument.files.isNotEmpty
                      ? Get.to(() => PreviewScreen(
                            // fileUrl: vehicleDocument.fileUrl,
                            files: vehicleDocument.files,
                            docName: vehicleDocument.docName,
                            // fileType: vehicleDocument.fileType,
                          ))
                      : null,
                  child: Padding(
                    padding: const EdgeInsets.only(right: AppInsets.s12),
                    child: Container(
                      padding:
                          const EdgeInsets.symmetric(vertical: AppInsets.s12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius:
                            BorderRadius.all(Radius.circular(AppInsets.s4)),
                        border: Border.all(
                          color: ColorConstants.borderColor,
                          width: AppInsets.s1,
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: AppInsets.pageMargin),
                            child: Container(
                              padding: const EdgeInsets.all(AppInsets.s6),
                              width: AppInsets.s56,
                              height: AppInsets.s56,
                              decoration: BoxDecoration(
                                color: ColorConstants.grayBackGroundColor,
                                borderRadius:
                                    BorderRadius.circular(AppInsets.s4),
                              ),
                              child: UniversalImage(
                                vehicleDocument.files.isNotEmpty
                                    ? vehicleDocument.files.first.fileType
                                                .toLowerCase() ==
                                            'pdf'
                                        ? SvgsConstants.document
                                        : SvgsConstants.image
                                    : SvgsConstants.image,
                                color: primaryColor,
                                height: AppInsets.s24,
                                width: AppInsets.s24,
                              ),
                            ),
                          ),
                          const SizedBox(width: AppInsets.s16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  vehicleDocument.docName.toUpperCase(),
                                  style: TextStyle(
                                    fontSize: AppInsets.s20,
                                    color: ColorConstants.black,
                                    fontWeight: FontWeight.w600,
                                    fontFamily: AppFonts.lexend,
                                  ),
                                  maxLines: 2,
                                  textAlign: TextAlign.start,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  (vehicleDocument.fieldData
                                              ?.firstWhereOrNull((field) =>
                                                  field.title.toLowerCase() ==
                                                  'expiry date')
                                              ?.value as Timestamp?)
                                          ?.toDate()
                                          .goodDayDate() ??
                                      '-'.toUpperCase(),
                                  style: TextStyle(
                                    fontSize: AppInsets.s12,
                                    color: ColorConstants.black,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: AppFonts.lexend,
                                  ),
                                  maxLines: 1,
                                  textAlign: TextAlign.start,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          if (vehicleDocument.files.isNotEmpty) ...[
                            InkWell(
                              onTap: () => _homeController.redirectUrl(
                                  vehicleDocument.files.first.fileUrl),
                              child: Icon(
                                CupertinoIcons.cloud_download,
                                color: primaryColor,
                                size: AppInsets.s24,
                              ),
                            ),
                            const SizedBox(width: AppInsets.s16),
                            Icon(
                              Icons.keyboard_arrow_right,
                              color: primaryColor,
                              size: AppInsets.s24,
                            ),
                          ],
                          const SizedBox(width: AppInsets.s8),
                        ],
                      ),
                    ),
                  ),
                );
              },
              separatorBuilder: (BuildContext context, int index) {
                return const SizedBox(height: AppInsets.s16);
              },
            );
          } else {
            return Center(child: CircularProgressIndicator());
          }
        });
  }

  Future<void> showTaskSelectionDialog(BuildContext context) async {
    List<String> taskNames = [];
    String? selectedTaskName;
    String? errorText;

    try {
      final taskCollection =
          await FBFireStore.tasks.where('isVehicle', isEqualTo: true).get();
      taskNames = taskCollection.docs
          .map((doc) => TaskModel.fromSnap(doc).taskName.toUpperCase())
          .toList();
    } catch (e) {
      debugPrint("Error loading task names: $e");
    }
    final dropdownController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            title: const Text('Document Type'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DropdownMenu<String>(
                  enableSearch: true,
                  requestFocusOnTap: false,
                  controller: dropdownController,
                  onSelected: (String? newTaskName) {
                    setState(() {
                      selectedTaskName = newTaskName;
                      errorText = null; // Clear error on selection
                    });
                  },
                  dropdownMenuEntries: taskNames.map((taskName) {
                    return DropdownMenuEntry<String>(
                      value: taskName,
                      label: taskName.toUpperCase(),
                    );
                  }).toList(),
                ),
                if (errorText != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      errorText!,
                      style: const TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  if (selectedTaskName == null || selectedTaskName!.isEmpty) {
                    setState(() {
                      errorText = 'Please select a document type.';
                    });
                  } else {
                    Navigator.of(context).pop();
                    showTaskDetailsDialog(context, selectedTaskName!);
                  }
                },
                child: const Text('Save'),
              ),
            ],
          );
        });
      },
    );
  }

  Future<void> showTaskDetailsDialog(
      BuildContext context, String taskName) async {
    try {
      List<SelectedFile?> selectedFile = [];
      bool loading = false;
      final Map<String, TextEditingController> controllers = {};
      final Map<String, String?> errors = {};

      final task = taskModels.firstWhere(
        (taskModel) =>
            taskModel.taskName.toLowerCase() == taskName.toLowerCase(),
      );
      for (var detail in task.taskDetails) {
        controllers[detail.title] = TextEditingController(text: "");
        errors[detail.title] = null;
      }
      showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState2) {
              return AlertDialog(
                title: const Text('Add Documents'),
                content: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  spacing: 20,
                  children: [
                    ...task.taskDetails.map((taskDetail) {
                      final controller = controllers[taskDetail.title]!;
                      final errorText = errors[taskDetail.title];

                      switch (taskDetail.type.toLowerCase()) {
                        case 'textfield':
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextFormField(
                                controller: controller,
                                onChanged: (newValue) {
                                  taskDetail.value = newValue;
                                  if (newValue.trim().isNotEmpty) {
                                    setState2(() {
                                      errors[taskDetail.title] = null;
                                    });
                                  }
                                },
                                cursorHeight: 20,
                                decoration: inpDecor().copyWith(
                                  labelText:
                                      'Enter value for ${taskDetail.value}',
                                  errorText: errorText,
                                ),
                              ),
                            ],
                          );
                        case 'datepicker':
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextFormField(
                                controller: controller,
                                decoration: inpDecor().copyWith(
                                  labelText:
                                      'Select a date for ${taskDetail.value}',
                                ),
                                onTap: () async {
                                  FocusScope.of(context).requestFocus(
                                      FocusNode()); // Dismiss keyboard
                                  DateTime? selectedDate = await showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now(),
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime(2101),
                                  );
                                  if (selectedDate != null) {
                                    final formatted =
                                        "${selectedDate.toLocal()}"
                                            .split(' ')[0];
                                    controller.text = formatted;
                                    taskDetail.value = formatted;
                                    setState2(() {
                                      errors[taskDetail.title] = null;
                                    });
                                  }
                                },
                                readOnly: true,
                              ),
                            ],
                          );
                        case 'expirydate':
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextFormField(
                                controller: controller,
                                decoration: inpDecor().copyWith(
                                  labelText:
                                      'Select a date for ${taskDetail.title}',
                                  errorText: errorText,
                                ),
                                onTap: () async {
                                  FocusScope.of(context).requestFocus(
                                      FocusNode()); // Dismiss keyboard
                                  DateTime? selectedDate = await showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now(),
                                    firstDate: DateTime(2000),
                                    lastDate: DateTime(2101),
                                  );
                                  if (selectedDate != null) {
                                    final formatted =
                                        "${selectedDate.toLocal()}"
                                            .split(' ')[0];
                                    controller.text = formatted;
                                    taskDetail.value = selectedDate;
                                    setState2(() {
                                      errors[taskDetail.title] = null;
                                    });
                                  }
                                },
                                readOnly: true,
                              ),
                              const SizedBox(height: 10),
                            ],
                          );
                        default:
                          return const SizedBox();
                      }
                    }),
                    InkWell(
                      onTap: () async {
                        try {
                          final fileUrl = await ImagePickerService()
                              .pickFile(context, true);
                          if (fileUrl.isNotEmpty) {
                            selectedFile.addAll(fileUrl);
                          }
                          setState2(() {});
                        } catch (e) {
                          debugPrint("Failed to pick doc: $e");
                          setState2(() {
                            loading = false;
                          });
                        }
                      },
                      borderRadius: BorderRadius.circular(3),
                      child: Container(
                        padding: EdgeInsets.all(10),
                        width: double.maxFinite,
                        height: 150,
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(7),
                            // color: const Color.fromARGB(9, 0, 0, 0),
                            color: Colors.transparent),
                        child: selectedFile.isEmpty
                            ? const Icon(Icons.file_copy_outlined)
                            : Wrap(
                                spacing: 10,
                                runSpacing: 10,
                                children: [
                                  ...List.generate(
                                    selectedFile.length,
                                    (index) {
                                      return selectedFile[index] == null
                                          ? const Center(
                                              child: Icon(
                                                  Icons.file_copy_outlined))
                                          : selectedFile[index]?.type == 'image'
                                              ? SizedBox(
                                                  height: 50,
                                                  width: 50,
                                                  child: Image.memory(
                                                    selectedFile[index]!
                                                        .uInt8List,
                                                    width: double.maxFinite,
                                                    fit: BoxFit.cover,
                                                  ),
                                                )
                                              : Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  spacing: 8,
                                                  children: [
                                                    const Icon(
                                                        Icons.picture_as_pdf),
                                                    Text(selectedFile[index]
                                                            ?.name ??
                                                        "")
                                                  ],
                                                );
                                    },
                                  ),
                                  SizedBox(
                                      height: 50,
                                      width: 50,
                                      child:
                                          Icon(Icons.add, color: Colors.black))
                                ],
                              ),
                      ),
                    ),
                    if (selectedFile.isEmpty)
                      Text(
                        "Please attach a file.",
                        style: TextStyle(color: Colors.red, fontSize: 12),
                      ),
                    // ElevatedButton(
                    //     style: ElevatedButton.styleFrom(
                    //       backgroundColor: primaryColor,
                    //       elevation: 0,
                    //       foregroundColor: Colors.white,
                    //       shape: RoundedRectangleBorder(
                    //           borderRadius: BorderRadius.circular(4)),
                    //       // padding: const EdgeInsets.fromLTRB(5, 15, 10, 15),
                    //     ),
                    //     onPressed: () {
                    //       fileCount++;
                    //       setState2(() {});
                    //     },
                    //     child: Text("Add Image"))
                  ],
                ),
                actions: loading
                    ? [
                        Center(
                          child: SizedBox(
                            height: 25,
                            width: 25,
                            child: CircularProgressIndicator(
                              strokeWidth: 2.5,
                            ),
                          ),
                        )
                      ]
                    : [
                        TextButton(
                          onPressed: () async {
                            bool hasError = false;

                            for (var detail in task.taskDetails) {
                              final value = (detail.type == "ExpiryDate")
                                  ? Timestamp.fromDate(detail.value)
                                  : controllers[detail.title]?.text.trim();
                              if (value == null) {
                                errors[detail.title] = 'This field is required';
                                hasError = true;
                              } else {
                                errors[detail.title] = null;
                                detail.value = value;
                              }
                            }

                            for (var element in selectedFile) {
                              if (element == null) {
                                hasError = true;
                                break;
                              }
                            }

                            setState2(() {});

                            if (hasError) return;

                            try {
                              setState2(() {
                                loading = true;
                              });

                              // Upload file if selected
                              // final fileUrl = selectedFile != null
                              //     ? await uploadVehicleDocs(selectedFile!)
                              //     : "";
                              final uuid = Uuid();
                              final documentId = uuid.v4();
                              Map fielddataMap = {};
                              for (var task in task.taskDetails) {
                                fielddataMap.addEntries(task.toJson().entries);
                              }
                              List<Map> files = [];
                              for (var element in selectedFile) {
                                final fileUrl = element != null
                                    ? await uploadUserDocs(element)
                                    : "";
                                files.add({element?.type: fileUrl});
                              }

                              final Map<String, dynamic> newTaskDoc = {
                                'documentId': documentId,
                                'docName': task.taskName,
                                // 'fileUrl': fileUrl,
                                // 'fileType': selectedFile != null
                                //     ? selectedFile!.type
                                //     : "",
                                "isDeleted": false,
                                "files": files,
                                "isArchived": false,
                                "uId": widget.vehicleModel.userId,
                                "vehicleId": widget.vehicleModel.docId,
                                "vehicleDoc": true,
                                "vehicleNum": widget.vehicleModel.vehicleNumber,
                                'fieldData': fielddataMap,
                                "userName": _homeController.user?.name,
                                "userContact": _homeController.user?.number,
                              };

                              // Push the task doc to the selected vehicle document
                              // await FBFireStore.vehicles
                              //     .doc(widget.vehicleModel.docId)
                              //     .update({
                              //   'vehicleDocuments':
                              //       FieldValue.arrayUnion([newTaskDoc]),
                              // });
                              await FBFireStore.usersDocs.add(newTaskDoc);

                              await addRecentToFirestore(
                                  FBAuth.auth.currentUser?.uid ?? "",
                                  task.taskName);

                              setState2(() {
                                loading = false;
                              });

                              if (context.mounted) {
                                // Locally update the vehicleModel's document list
                                setState(() {
                                  // widget.vehicleModel.vehicleDocuments
                                  //     .add(VehicleDocumentModel(
                                  //   documentId: task.docId,
                                  //   docName: task.taskName,
                                  //   fileUrl: fileUrl ?? '',
                                  //   fileType: selectedFile?.type ?? "",
                                  //   fieldData: task.taskDetails,
                                  //   isDeleted: false,
                                  // ));
                                });

                                Navigator.of(context).pop(); // Close the dialog
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text("Document added to vehicle."),
                                  ),
                                );
                              }
                            } catch (e) {
                              debugPrint("Error saving document: $e");
                              setState2(() {
                                loading = false;
                              });
                            }
                          },
                          child: const Text("Add"),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: const Text("Cancel"),
                        ),
                      ],
              );
            },
          );
        },
      );
    } catch (e) {
      debugPrint("Error loading task details: $e");
    }
  }

  Future<void> addRecentToFirestore(
    String userId,
    String message,
  ) async {
    try {
      UserModel? user = await StorageService.getUserFromPrefs();
      DateTime now = DateTime.now();
      String formattedDate = DateFormat('dd/MM/yyyy - hh:mm').format(now);

      await FirebaseFirestore.instance.collection('recents').add({
        'userid': userId,
        'name': user?.name,
        'email': user?.email,
        'number': user?.number,
        'message':
            'Data updated: $formattedDate - \n ${user?.name} - ${user?.number} - $message',
        'createdAt': FieldValue.serverTimestamp(),
      });
      debugPrint("✅ Recent contact added.");
    } catch (e) {
      debugPrint("❌ Failed to add recent: $e");
    }
  }
}
