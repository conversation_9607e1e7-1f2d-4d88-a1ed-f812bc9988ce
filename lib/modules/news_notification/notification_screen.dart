import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/web/models/notification_model.dart';

import '../../shared/constants/colors.dart';
import '../../web/shared/theme.dart';

class NotificationScreen extends StatelessWidget {
  const NotificationScreen({
    super.key,
    required this.notificationList,
  });

  final List<NotificationModel> notificationList;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          centerTitle: true,
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: Get.back,
            child: Icon(
              Icons.arrow_back,
              color: ColorConstants.black,
              size: AppInsets.s24,
            ),
          ),
          title: Text(
            "News and updates",
            style: TextStyle(
              fontSize: AppInsets.s18,
              color: ColorConstants.black,
              fontWeight: FontWeight.w600,
              fontFamily: AppFonts.lexend,
            ),
          ),
        ),
        body: Safe<PERSON>rea(
          child: Column(
            children: [
              Divider(
                color: ColorConstants.grayBackGroundColor,
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppInsets.pageMargin,
                    vertical: AppInsets.s8,
                  ),
                  child: _myDocumentList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _myDocumentList() {
    return ListView.separated(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(vertical: AppInsets.s16),
      itemCount: notificationList.length,
      itemBuilder: (context, index) {
        NotificationModel newsModel = notificationList[index];

        return Padding(
          padding: const EdgeInsets.only(right: AppInsets.s12),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: AppInsets.s12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(AppInsets.s4)),
              border: Border.all(
                color: ColorConstants.borderColor,
                width: AppInsets.s1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppInsets.pageMargin),
                  child: Icon(
                    Icons.notifications_none,
                    color: primaryColor,
                    size: AppInsets.s32,
                  ),
                ),
                const SizedBox(width: AppInsets.s16),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      newsModel.title.toUpperCase(),
                      style: TextStyle(
                        fontSize: AppInsets.s16,
                        color: ColorConstants.black,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppFonts.lexend,
                      ),
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      newsModel.desc,
                      style: TextStyle(
                        fontSize: AppInsets.s14,
                        color: ColorConstants.black,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppFonts.lexend,
                      ),
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                const SizedBox(width: AppInsets.s8),
              ],
            ),
          ),
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return const SizedBox(height: AppInsets.s16);
      },
    );
  }
}
