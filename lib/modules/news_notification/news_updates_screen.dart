import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/modules/news_notification/news_details.dart';
import 'package:mass_ibs/shared/constants/constants.dart';
import 'package:mass_ibs/web/models/news_model.dart';

import '../../web/shared/theme.dart';

class NewsUpdatesScreen extends StatelessWidget {
  const NewsUpdatesScreen({
    super.key,
    required this.newsList,
  });

  final List<NewsModel> newsList;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          centerTitle: true,
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: Get.back,
            child: Icon(
              Icons.arrow_back,
              color: ColorConstants.black,
              size: AppInsets.s24,
            ),
          ),
          title: Text(
            "News and updates",
            style: TextStyle(
              fontSize: AppInsets.s18,
              color: ColorConstants.black,
              fontWeight: FontWeight.w600,
              fontFamily: AppFonts.lexend,
            ),
          ),
        ),
        body: SafeArea(
          child: Column(
            children: [
              Divider(
                color: ColorConstants.grayBackGroundColor,
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppInsets.pageMargin,
                    vertical: AppInsets.s8,
                  ),
                  child: _myDocumentList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _myDocumentList() {
    return ListView.separated(
      shrinkWrap: true,
      padding: const EdgeInsets.symmetric(vertical: AppInsets.s16),
      itemCount: newsList.length,
      itemBuilder: (context, index) {
        NewsModel newsModel = newsList[index];

        return GestureDetector(
          onTap: () =>
              Get.to(() => NewsUpdatesDetailsScreen(newsModel: newsModel)),
          child: Padding(
            padding: const EdgeInsets.only(right: AppInsets.s12),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: AppInsets.s12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(AppInsets.s4)),
                border: Border.all(
                  color: ColorConstants.borderColor,
                  width: AppInsets.s1,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: AppInsets.pageMargin),
                    child: Container(
                      padding: const EdgeInsets.all(AppInsets.s6),
                      width: AppInsets.s56,
                      height: AppInsets.s56,
                      decoration: BoxDecoration(
                        color: ColorConstants.grayBackGroundColor,
                        borderRadius: BorderRadius.circular(AppInsets.s4),
                      ),
                      child: Icon(
                        Icons.newspaper,
                        color: primaryColor,
                        size: AppInsets.s32,
                      ),
                    ),
                  ),
                  const SizedBox(width: AppInsets.s16),
                  Expanded(
                    child: Text(
                      newsModel.title.toUpperCase(),
                      style: TextStyle(
                        fontSize: AppInsets.s16,
                        color: ColorConstants.black,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppFonts.lexend,
                      ),
                      maxLines: 2,
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (newsModel.description.isNotEmpty) ...[
                    Icon(
                      Icons.keyboard_arrow_right,
                      color: primaryColor,
                      size: AppInsets.s24,
                    ),
                  ],
                  const SizedBox(width: AppInsets.s8),
                ],
              ),
            ),
          ),
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return const SizedBox(height: AppInsets.s16);
      },
    );
  }
}
