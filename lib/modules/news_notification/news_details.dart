import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/shared/constants/constants.dart';
import 'package:mass_ibs/web/models/news_model.dart';
import 'package:webview_flutter/webview_flutter.dart';

class NewsUpdatesDetailsScreen extends StatefulWidget {
  const NewsUpdatesDetailsScreen({
    super.key,
    required this.newsModel,
  });

  final NewsModel newsModel;

  @override
  State<NewsUpdatesDetailsScreen> createState() =>
      _NewsUpdatesDetailsScreenState();
}

class _NewsUpdatesDetailsScreenState extends State<NewsUpdatesDetailsScreen> {
  late final WebViewController _webViewController;

  @override
  void initState() {
    super.initState();
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Optional: show loading progress
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {},
          onHttpError: (HttpResponseError error) {},
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadHtmlString(widget.newsModel.description);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          centerTitle: true,
          backgroundColor: Colors.white,
          leading: InkWell(
            onTap: Get.back,
            child: Icon(
              Icons.arrow_back,
              color: ColorConstants.black,
              size: AppInsets.s24,
            ),
          ),
          title: Text(
            widget.newsModel.title.toUpperCase(),
            style: TextStyle(
              fontSize: AppInsets.s18,
              color: ColorConstants.black,
              fontWeight: FontWeight.w600,
              fontFamily: AppFonts.lexend,
            ),
          ),
        ),
        body: SafeArea(
          child: Column(
            children: [
              Divider(
                color: ColorConstants.grayBackGroundColor,
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppInsets.pageMargin,
                    vertical: AppInsets.s8,
                  ),
                  child: WebViewWidget(controller: _webViewController),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
