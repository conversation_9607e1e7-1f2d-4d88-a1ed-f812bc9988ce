import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/web/models/news_model.dart';
import 'package:mass_ibs/web/models/setting_model.dart';
import 'package:mass_ibs/web/models/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../shared/firebase.dart';
import '../../shared/services/push_notification_service.dart';
import '../../web/models/banner_model.dart';
import '../../web/models/notification_model.dart';
import '../../web/models/task_model.dart';
import '../../web/models/vehicle_model.dart';

class HomeController extends GetxController {
  HomeController();

  final PushNotificationService _pushService = PushNotificationService();
  SettingModel? setting;
  UserModel? user;
  List<TaskModel> tasks = [];
  List<BannerModel> banners = [];
  List<VehicleModel> vehicles = [];
  List<NewsModel> newsList = [];
  List<NotificationModel> notificationList = [];
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? userStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? tasksStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? bannersStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? vehiclesStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? newsStream;
  StreamSubscription<QuerySnapshot<Map<String, dynamic>>>? notificationStream;
  StreamSubscription<DocumentSnapshot<Map<String, dynamic>>>? settingstream;

  RxString? selectedTaskName = "".obs;
  final RxList<String> taskNames = <String>[].obs;
  final RxList<TaskModel> taskModels = <TaskModel>[].obs;
  bool loggedIn = false;

  @override
  void onInit() {
    userAuthStream();
    getSetting();
    getBanners();
    getNews();

    super.onInit();
  }

  userAuthStream() async {
    FirebaseAuth.instance.authStateChanges().listen((event) {
      loggedIn = event != null;
      if (loggedIn) {
        getUser();
        getTasks();
        getNotifications();
        _loadTaskModels();
      }
    });
  }

  getUser() async {
    // user = await StorageService.getUserFromPrefs();
    await getUserByIdStream(FBAuth.auth.currentUser?.uid ?? "");
    await getVehiclesByUserId(FBAuth.auth.currentUser?.uid ?? "");
    await _initializePush(FBAuth.auth.currentUser?.uid ?? "");
    update();
  }

  _initializePush(String userId) async {
    await _pushService.initialize(userId);
    await _pushService.subscribeToTopics(['global', 'test']);
  }

  getUserByIdStream(String userId) {
    // Cancel previous stream if any
    userStream?.cancel();

    try {
      userStream = FBFireStore.users.doc(userId).snapshots().listen((doc) {
        if (doc.exists && doc.data() != null) {
          user = UserModel.fromDocSnap(doc);
          update();
        } else {
          debugPrint("⚠️ User not found with ID: $userId");
          update();
        }
      });
    } catch (e) {
      debugPrint("❌ Error in getUserByIdStream: ${e.toString()}");
      update();
    }
  }

  getSetting() async {
    try {
      settingstream?.cancel();
      settingstream = FBFireStore.setting.snapshots().listen((event) {
        setting = SettingModel.fromSnapshot(event);

        update();
      });
    } catch (e) {
      debugPrint('Error fetching tasks: ${e.toString()}');
    }
  }

  getTasks() async {
    try {
      tasksStream?.cancel();
      tasksStream = FBFireStore.tasks.snapshots().listen((event) {
        tasks = event.docs.map((e) => TaskModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint('Error fetching tasks: ${e.toString()}');
    }
  }

  getBanners() async {
    try {
      bannersStream?.cancel();
      bannersStream = FBFireStore.banners.snapshots().listen((event) {
        banners = event.docs.map((e) => BannerModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  getNotifications() async {
    try {
      notificationStream?.cancel();
      notificationStream =
          FBFireStore.notifications.snapshots().listen((event) {
        notificationList =
            event.docs.map((e) => NotificationModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint('Error fetching notifications: ${e.toString()}');
    }
  }

  getVehiclesByUserId(String userId) async {
    try {
      // Cancel previous stream if it exists
      vehiclesStream?.cancel();

      // Set up a new real-time listener for vehicles owned by a specific user
      vehiclesStream = FBFireStore.vehicles
          .where('userId', isEqualTo: userId)
          .snapshots()
          .listen((event) {
        vehicles = event.docs.map((e) {
          return VehicleModel.fromSnap(e);
        }).toList();
        vehicles.sort((a, b) {
          final aDate = a.createdAt?.toDate();
          final bDate = b.createdAt?.toDate();
          if (aDate == null && bDate == null) return 0;
          if (aDate == null) return 1;
          if (bDate == null) return -1;
          return bDate.compareTo(aDate); // latest first
        });

        // Notify UI or other listeners
        update();
      });
    } catch (e) {
      debugPrint("❌ Error in getVehiclesByUserId: ${e.toString()}");
    }
  }

  getNews() async {
    try {
      newsStream?.cancel();
      newsStream = FBFireStore.news.snapshots().listen((event) {
        newsList = event.docs.map((e) => NewsModel.fromSnap(e)).toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  void signOut() {
    var prefs = Get.find<SharedPreferences>();
    prefs.clear();
  }

  Future<bool> redirectUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      final success = await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
      return success;
    } else {
      return false;
    }
  }

  Future<void> _loadTaskModels() async {
    try {
      final taskCollection = await FBFireStore.tasks.get();
      List<TaskModel> fetchedTaskModels = [];

      for (var doc in taskCollection.docs) {
        final taskModel = TaskModel.fromSnap(doc); // Convert to TaskModel

        // Handle empty taskDetails safely
        List<TaskDetailModel> taskDetails = [];
        if (doc.data()['fields'] != null) {
          taskDetails = taskDetails = (doc.data()['fields'] as List<dynamic>)
              .map((e) => TaskDetailModel.fromJson(
                  e['title'], e as Map<String, dynamic>))
              .toList();
        }

        // Directly assigning the updated task details
        taskModel.taskDetails = taskDetails;

        fetchedTaskModels.add(taskModel); // Add to the list
      }
      taskModels.assignAll(fetchedTaskModels);
      selectedTaskName?.value =
          taskModels.isNotEmpty ? taskModels.first.taskName : '';
    } catch (e) {
      debugPrint("Error loading task models: $e");
    }
    debugPrint("Task models: $taskModels");
  }
}
