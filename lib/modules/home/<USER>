import 'dart:io';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mass_ibs/modules/auth/login_screen.dart';
import 'package:mass_ibs/modules/home/<USER>';
import 'package:mass_ibs/modules/news_notification/news_updates_screen.dart';
import 'package:mass_ibs/modules/payment/payment_screen.dart';
import 'package:mass_ibs/modules/preview_doc/preview_screen.dart';
import 'package:mass_ibs/modules/vehicle/vehicle_screen.dart';
import 'package:mass_ibs/shared/shared.dart';

import 'package:mass_ibs/web/models/user_docs_model.dart';
import 'package:mass_ibs/web/shared/theme.dart';
import 'package:share_plus/share_plus.dart';
import 'package:uuid/uuid.dart';

import '../../shared/firebase.dart';
import '../../shared/widgets/universal_image.dart';
import '../../web/models/banner_model.dart';
import '../../web/models/task_model.dart';
import '../../web/models/user_model.dart';
import '../../web/models/vehicle_model.dart';
import '../../web/services/image_picker.dart';
import '../../web/shared/methods.dart';
import '../news_notification/notification_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int currentIndex = 0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // Get.lazyPut(() => HomeController());
    Get.put(HomeController());
  }

  final HomeController _homeController = Get.put(HomeController());

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          titleSpacing: 0,
          backgroundColor: Colors.white,
          title: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  left: AppInsets.s16,
                  right: AppInsets.s10,
                ),
                child: Container(
                  height: AppInsets.s32,
                  width: AppInsets.s32,
                  decoration: BoxDecoration(
                    color: ColorConstants.secondaryAppColor,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      _homeController.user != null &&
                              _homeController.user!.name.isNotEmpty
                          ? _homeController.user!.name[0].toUpperCase()
                          : '?',
                      style: TextStyle(
                        fontSize: AppInsets.s20,
                        fontWeight: FontWeight.w600,
                        fontFamily: AppFonts.lexend,
                      ),
                    ),
                  ),
                ),
              ),
              GetBuilder<HomeController>(builder: (homeController) {
                return Expanded(
                  child: Text(
                    overflow: TextOverflow.ellipsis,
                    homeController.user != null &&
                            homeController.user!.name.isNotEmpty
                        ? homeController.user!.name.toUpperCase()
                        : '',
                    style: TextStyle(
                      fontSize: AppInsets.s16,
                      color: ColorConstants.black,
                      fontWeight: FontWeight.w600,
                      fontFamily: AppFonts.lexend,
                    ),
                  ),
                );
              }),
            ],
          ),
          actions: [
            InkWell(
              onTap: () => Get.to(
                () => PaymentScreen(),
              ),
              child: Padding(
                padding: const EdgeInsets.only(
                  right: AppInsets.s16,
                ),
                child: UniversalImage(
                  ImageConstants.payment,
                  height: AppInsets.s24,
                ),
              ),
            ),
            InkWell(
              onTap: () => Get.to(
                () => NotificationScreen(
                  notificationList: _homeController.notificationList,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.only(
                  right: AppInsets.s16,
                ),
                child: Badge(
                  smallSize: AppInsets.s10,
                  backgroundColor: ColorConstants.redColor,
                  child: Icon(
                    Icons.notifications_none,
                    size: AppInsets.s24,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return StatefulBuilder(
                      builder: (context, setState2) {
                        return AlertDialog(
                          title: const Text("Alert"),
                          content:
                              const Text("Are you sure you want to logout"),
                          actions: [
                            TextButton(
                                onPressed: () async {
                                  StorageService.logout();
                                  Get.offAll(
                                    () => LoginScreen(),
                                  );
                                },
                                child: const Text('Yes')),
                            TextButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                child: const Text('No')),
                          ],
                        );
                      },
                    );
                  },
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(
                  right: AppInsets.s16,
                ),
                child: Icon(
                  Icons.logout,
                  size: AppInsets.s24,
                  color: Colors.black,
                ),
              ),
            ),
          ],
        ),
        body: SafeArea(
          child: _buildWidget(),
        ),
      ),
    );
  }

  Widget _buildWidget() {
    return Column(
      children: [
        Divider(
          color: ColorConstants.grayBackGroundColor,
          // thickness: 1,
          // height: 1,
        ),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                GetBuilder<HomeController>(
                  builder: (homeController) {
                    final bannersList = homeController.banners;

                    if (bannersList.isEmpty) {
                      return Center(child: Text('No banners found.'));
                    }

                    return Column(
                      children: [
                        const SizedBox(height: 8),
                        Padding(
                          padding: EdgeInsets.symmetric(
                            horizontal: AppInsets.pageMargin,
                            vertical: AppInsets.s8,
                          ),
                          child: Column(
                            children: [
                              _myInsuranceList(),
                              _myDocumentList(),
                            ],
                          ),
                        ),
                        // CarouselSlider.builder(
                        //   itemCount: bannersList.length,
                        //   itemBuilder: (context, index, realIdx) {
                        //     final banner = bannersList[index];
                        //     return InkWell(
                        //       onTap: () async {
                        //         if (banner.link.isNotEmpty) {
                        //           bool result = await _homeController
                        //               .redirectUrl(banner.link);
                        //           if (!result) {
                        //             showImageDialog(context, banner);
                        //           }
                        //         } else {
                        //           showImageDialog(context, banner);
                        //         }
                        //       },
                        //       child: Padding(
                        //         padding: EdgeInsets.symmetric(
                        //           horizontal: AppInsets.pageMargin,
                        //           vertical: AppInsets.s8,
                        //         ),
                        //         child: ClipRRect(
                        //           borderRadius: BorderRadius.circular(12),
                        //           child: UniversalImage(
                        //             banner.image,
                        //             fit: BoxFit.cover,
                        //             width: double.infinity,
                        //           ),
                        //         ),
                        //       ),
                        //     );
                        //   },
                        //   options: CarouselOptions(
                        //     onPageChanged: (index, reason) {
                        //       currentIndex = index;
                        //       setState(() {});
                        //     },
                        //     autoPlay: true,
                        //     enlargeCenterPage: true,
                        //     viewportFraction: 1,
                        //     aspectRatio: 5 / 4,
                        //     autoPlayInterval: Duration(seconds: 4),
                        //   ),
                        // ),
                        ...List.generate(
                          bannersList.length,
                          (index) {
                            final banner = bannersList[index];
                            return BannerUI(banner: banner);
                          },
                        ),
                        // Padding(
                        //   padding: const EdgeInsets.all(10.0),
                        //   child: Row(
                        //     spacing: 3,
                        //     mainAxisAlignment: MainAxisAlignment.center,
                        //     children: [
                        //       ...List.generate(
                        //         bannersList.length,
                        //         (index) {
                        //           return Container(
                        //             decoration: BoxDecoration(
                        //               border: Border.all(
                        //                 color: Color(0xffACACAC),
                        //               ),
                        //               borderRadius: BorderRadius.circular(5),
                        //               color: index != currentIndex
                        //                   ? Colors.white
                        //                   : Color(0xffACACAC),
                        //             ),
                        //             width: index == currentIndex ? 40 : 10,
                        //             height: 6,
                        //           );
                        //         },
                        //       )
                        //     ],
                        //   ),
                        // ),
                      ],
                    );
                  },
                ),
                Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppInsets.pageMargin,
                    vertical: AppInsets.s8,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.max,
                    spacing: AppInsets.s12,
                    children: [
                      const SizedBox(height: 8),
                      // _myInsuranceList(),
                      // _myDocumentList(),
                      Text(
                        "News and updates",
                        style: TextStyle(
                          fontSize: AppInsets.s16,
                          color: ColorConstants.black,
                          fontWeight: FontWeight.w500,
                          fontFamily: AppFonts.lexend,
                        ),
                      ),
                      InkWell(
                        onTap: () => Get.to(() => NewsUpdatesScreen(
                              newsList: _homeController.newsList,
                            )),
                        child: Container(
                          clipBehavior: Clip.antiAlias,
                          padding: const EdgeInsets.symmetric(
                              vertical: AppInsets.s12),
                          decoration: BoxDecoration(
                            color: Color(0xffECE3F4),
                            borderRadius: BorderRadius.all(
                                Radius.circular(AppInsets.s10)),
                            border: Border.all(
                              color: Color(0xffECE3F4),
                              width: AppInsets.s1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: AppInsets.pageMargin),
                                child: Container(
                                    clipBehavior: Clip.antiAlias,
                                    padding: const EdgeInsets.all(AppInsets.s6),
                                    width: AppInsets.s56,
                                    height: AppInsets.s56,
                                    decoration: BoxDecoration(
                                      color: ColorConstants.white,
                                      borderRadius:
                                          BorderRadius.circular(AppInsets.s4),
                                    ),
                                    child: Image.asset('assets/images/news.png')
                                    //  Icon(
                                    //   Icons.newspaper,
                                    //   color: primaryColor,
                                    //   size: AppInsets.s32,
                                    // ),
                                    ),
                              ),
                              SizedBox(
                                width: AppInsets.s10,
                              ),
                              Expanded(
                                child: Text(
                                  'News',
                                  // 'View latest news and updates',
                                  style: TextStyle(
                                    fontSize: AppInsets.s16,
                                    color: ColorConstants.black,
                                    fontWeight: FontWeight.w600,
                                    // fontWeight: FontWeight.w500,
                                    fontFamily: AppFonts.lexend,
                                  ),
                                  maxLines: 2,
                                  textAlign: TextAlign.start,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_sharp,
                                // Icons.keyboard_arrow_right,
                                color: Colors.black,
                                // color: primaryColor,
                                size: AppInsets.s28,
                              ),
                              const SizedBox(width: AppInsets.s20),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: AppInsets.s12),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _myDocumentList() {
    return StreamBuilder(
        stream: FBFireStore.usersDocs
            .where('uId', isEqualTo: FBAuth.auth.currentUser?.uid)
            .where('vehicleDoc', isEqualTo: false)
            .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.hasError) {
            return Text("Facing some issue!!");
          } else if (snapshot.hasData) {
            List<UserDocsModel> userDocs = snapshot.data?.docs
                    .map((e) => UserDocsModel.fromSnapshot(e))
                    .toList() ??
                [];
            return GetBuilder<HomeController>(builder: (homeController) {
              if (homeController.user == null) {
                return CircularProgressIndicator();
              }
              // UserModel usersModel = homeController.user!;

              return Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "My Documents",
                        style: TextStyle(
                          fontSize: AppInsets.s16,
                          color: ColorConstants.black,
                          fontWeight: FontWeight.w500,
                          fontFamily: AppFonts.lexend,
                        ),
                      ),
                      Spacer(),
                      if (homeController.setting?.userAddDocPermission ??
                          true) ...[
                        InkWell(
                          onTap: () {
                            showTaskSelectionDialog(context);
                          },
                          child: Icon(
                            Icons.add_box_rounded,
                            size: AppInsets.s24,
                            color: ColorConstants.secondaryAppColor,
                          ),
                        ),
                        SizedBox(width: AppInsets.s2),
                        InkWell(
                          onTap: () {
                            showTaskSelectionDialog(context);
                          },
                          child: Text(
                            "Add Documents",
                            style: TextStyle(
                              fontSize: AppInsets.s16,
                              color: ColorConstants.secondaryAppColor,
                              fontWeight: FontWeight.w500,
                              fontFamily: AppFonts.lexend,
                            ),
                          ),
                        ),
                      ]
                    ],
                  ),
                  if (userDocs.isEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 15.0),
                      child: Container(
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                  color:
                                      const Color.fromARGB(255, 216, 216, 216),
                                  blurStyle: BlurStyle.outer,
                                  blurRadius: AppInsets.s10)
                            ],
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(10)),
                        height: 160,
                        width: double.infinity,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Image.asset(
                              'assets/images/user_document.png',
                              fit: BoxFit.cover,
                            ),
                            Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                    maxWidth: 160, maxHeight: 120),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Upload your Documents",
                                      style: TextStyle(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 20),
                                    ),
                                    Spacer(),
                                    ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                            padding: EdgeInsets.symmetric(
                                                vertical: 10, horizontal: 15),
                                            shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(3)),
                                            backgroundColor: Color(0xff4354A4),
                                            surfaceTintColor:
                                                Color(0xff4354A4)),
                                        onPressed: (homeController.setting
                                                    ?.userAddDocPermission ??
                                                true)
                                            ? () async {
                                                showTaskSelectionDialog(
                                                    context);
                                              }
                                            : () {},
                                        child: Text(
                                          "Upload documents",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.w600,
                                              fontSize: 12),
                                        )),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (userDocs.isNotEmpty)
                    SizedBox(
                      height: SizeConfig().screenWidth * 0.38,
                      child:

                          //  userDocs.isEmpty
                          //     ? Center(
                          //         child: Text(
                          //           "No Documents Uploaded",
                          //           style: TextStyle(
                          //             fontSize: AppInsets.s16,
                          //             color: ColorConstants.secondaryAppColor,
                          //             fontWeight: FontWeight.w500,
                          //             fontFamily: AppFonts.lexend,
                          //           ),
                          //         ),
                          //       )
                          //     :
                          ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding:
                            const EdgeInsets.symmetric(vertical: AppInsets.s16),
                        itemCount: userDocs.length,
                        itemBuilder: (context, index) {
                          return GestureDetector(
                            onTap: () => Get.to(() => PreviewScreen(
                                  files: userDocs[index].files,
                                  // fileUrl: userDocs[index].fileUrl,
                                  docName: userDocs[index].docName,
                                  // fileType: userDocs[index].fileType,
                                )),
                            child: Padding(
                              padding:
                                  const EdgeInsets.only(right: AppInsets.s12),
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: AppInsets.s12),
                                constraints: BoxConstraints(
                                  maxWidth: SizeConfig().screenWidth * 0.44,
                                  minWidth: SizeConfig().screenWidth * 0.44,
                                  minHeight: SizeConfig().screenWidth * 0.32,
                                  maxHeight: SizeConfig().screenWidth * 0.32,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.all(
                                      Radius.circular(AppInsets.s10)),
                                  boxShadow: [
                                    BoxShadow(
                                        color: const Color.fromARGB(
                                            255, 230, 230, 230),
                                        // spreadRadius: AppInsets.s36,
                                        blurRadius: AppInsets.s12,
                                        blurStyle: BlurStyle.outer)
                                  ],
                                  border: Border.all(
                                    color: const Color.fromARGB(
                                        255, 231, 231, 231),
                                    width: AppInsets.s1,
                                  ),
                                ),
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.only(
                                          left: AppInsets.s6),
                                      width: AppInsets.s56,
                                      // height: AppInsets.s40,
                                      decoration: BoxDecoration(
                                        // color: ColorConstants
                                        //     .grayBackGroundColor,
                                        borderRadius:
                                            BorderRadius.circular(AppInsets.s4),
                                      ),
                                      child: true
                                          ? Image.asset(
                                              fit: BoxFit.cover,
                                              "assets/images/user_document.png")
                                          : UniversalImage(
                                              SvgsConstants.document,
                                              height: AppInsets.s24,
                                              width: AppInsets.s24,
                                            ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: AppInsets.pageMargin),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            userDocs[index]
                                                .docName
                                                .toUpperCase(),
                                            style: TextStyle(
                                              fontSize: AppInsets.s16,
                                              color: ColorConstants.black,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: AppFonts.lexend,
                                            ),
                                            maxLines: 2,
                                            textAlign: TextAlign.start,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          Text(
                                            (userDocs[index]
                                                        .fieldData
                                                        ?.firstWhereOrNull(
                                                            (field) =>
                                                                field.title
                                                                    .toLowerCase() ==
                                                                'expiry date')
                                                        ?.value as Timestamp?)
                                                    ?.toDate()
                                                    .goodDayDate() ??
                                                '-'.toUpperCase(),
                                            style: TextStyle(
                                              fontSize: AppInsets.s12,
                                              color: ColorConstants.black,
                                              fontWeight: FontWeight.w500,
                                              fontFamily: AppFonts.lexend,
                                            ),
                                            maxLines: 1,
                                            textAlign: TextAlign.start,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    )
                ],
              );
            });
          } else {
            return Center(child: CircularProgressIndicator());
          }
        });
  }

  _myInsuranceList() {
    return GetBuilder<HomeController>(builder: (homeController) {
      List<VehicleModel> vehiclesList = homeController.vehicles;

      return Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                "My Vehicles",
                style: TextStyle(
                  fontSize: AppInsets.s16,
                  color: ColorConstants.black,
                  fontWeight: FontWeight.w500,
                  fontFamily: AppFonts.lexend,
                ),
              ),
              Spacer(),
              if (homeController.setting?.userAddDocPermission ?? true)
                InkWell(
                  onTap: () {
                    addVehicleForm(
                      context,
                      null,
                      FBAuth.auth.currentUser?.uid ?? "",
                    );
                  },
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Icon(
                        Icons.add_box_rounded,
                        size: AppInsets.s24,
                        color: ColorConstants.secondaryAppColor,
                      ),
                      SizedBox(width: AppInsets.s2),
                      Text(
                        "Add Vehicle",
                        style: TextStyle(
                          fontSize: AppInsets.s16,
                          color: ColorConstants.secondaryAppColor,
                          fontWeight: FontWeight.w500,
                          fontFamily: AppFonts.lexend,
                        ),
                      ),
                    ],
                  ),
                )
            ],
          ),
          if (homeController.vehicles.isEmpty) ...[
            SizedBox(height: 15),
            Stack(
              children: [
                Container(
                  clipBehavior: Clip.antiAlias,
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(10)),
                  height: 160,
                  width: double.infinity,
                  child: Image.asset(
                    'assets/images/vehcile.png',
                    fit: BoxFit.cover,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 160, maxHeight: 130),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Add your Vehicles",
                          style: TextStyle(
                              fontWeight: FontWeight.w600, fontSize: 20),
                        ),
                        Spacer(),
                        ElevatedButton(
                            style: ElevatedButton.styleFrom(
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(3)),
                                backgroundColor: Color(0xff4354A4),
                                surfaceTintColor: Color(0xff4354A4)),
                            onPressed:
                                (homeController.setting?.userAddDocPermission ??
                                        true)
                                    ? () async {
                                        addVehicleForm(
                                          context,
                                          null,
                                          FBAuth.auth.currentUser?.uid ?? "",
                                        );
                                      }
                                    : () {},
                            child: Text(
                              "Add Vehicle",
                              style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12),
                            )),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 10)
          ],
          if (vehiclesList.isNotEmpty)
            SizedBox(
              height: SizeConfig().screenWidth * 0.45,
              child:
                  // ? Center(
                  //     child: Text(
                  //       "No vehicles found",
                  //       style: TextStyle(
                  //         fontSize: AppInsets.s16,
                  //         color: ColorConstants.secondaryAppColor,
                  //         fontWeight: FontWeight.w500,
                  //         fontFamily: AppFonts.lexend,
                  //       ),
                  //     ),
                  //   )
                  // :
                  ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(vertical: AppInsets.s16),
                itemCount: vehiclesList.length,
                itemBuilder: (context, index) {
                  return VehicleCardMobile(

                      // key: ValueKey(DateTime.now()),
                      vehiclesList: vehiclesList[index]);
                },
              ),
            ),
        ],
      );
    });
  }

  Future<void> showTaskSelectionDialog(BuildContext context) async {
    List<String> taskNames = [];
    String? selectedTaskName;
    String? errorText;

    try {
      try {
        final taskCollection =
            await FBFireStore.tasks.where('isVehicle', isEqualTo: false).get();

        taskNames = taskCollection.docs
            .map((doc) => TaskModel.fromSnap(doc).taskName.toUpperCase())
            .toList();
      } catch (e) {
        debugPrint("Error loading task names: $e");
      }
    } catch (e) {
      debugPrint("Error loading task names: $e");
    }
    final dropdownController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(builder: (context, setState) {
          return AlertDialog(
            title: const Text('Document Type'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DropdownMenu<String>(
                  enableSearch: true,
                  requestFocusOnTap: false,
                  controller: dropdownController,
                  onSelected: (String? newTaskName) {
                    setState(() {
                      selectedTaskName = newTaskName;
                      errorText = null; // Clear error on selection
                    });
                  },
                  dropdownMenuEntries: taskNames.map((taskName) {
                    return DropdownMenuEntry<String>(
                      value: taskName,
                      label: taskName.toUpperCase(),
                    );
                  }).toList(),
                ),
                if (errorText != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      errorText!,
                      style: const TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  if (selectedTaskName == null || selectedTaskName!.isEmpty) {
                    setState(() {
                      errorText = 'Please select a document type.';
                    });
                  } else {
                    Navigator.of(context).pop();
                    showTaskDetailsDialog(context, selectedTaskName!);
                  }
                },
                child: const Text('Save'),
              ),
            ],
          );
        });
      },
    );
  }

  Future<void> showTaskDetailsDialog(
      BuildContext context, String taskName) async {
    try {
      List<SelectedFile?> selectedFile = [];
      bool loading = false;
      final Map<String, TextEditingController> controllers = {};
      final Map<String, String?> errors = {};
      final task = _homeController.taskModels.firstWhere(
        (taskModel) =>
            taskModel.taskName.toLowerCase() == taskName.toLowerCase(),
      );
      for (var detail in task.taskDetails) {
        controllers[detail.title] = TextEditingController(text: "");
        errors[detail.title] = null;
      }

      showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState2) {
              return AlertDialog(
                title: const Text('Add Documents'),
                content: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    spacing: 20,
                    children: [
                      ...task.taskDetails.map((taskDetail) {
                        final controller = controllers[taskDetail.title]!;
                        final errorText = errors[taskDetail.title];

                        switch (taskDetail.type.toLowerCase()) {
                          case 'textfield':
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextFormField(
                                  controller: controller,
                                  onChanged: (newValue) {
                                    taskDetail.value = newValue;
                                    if (newValue.trim().isNotEmpty) {
                                      setState2(() {
                                        errors[taskDetail.title] = null;
                                      });
                                    }
                                  },
                                  decoration: inpDecor().copyWith(
                                    labelText:
                                        'Enter value for ${taskDetail.value}',
                                    errorText: errorText,
                                  ),
                                ),
                              ],
                            );
                          case 'datepicker':
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextFormField(
                                  controller: controller,
                                  decoration: inpDecor().copyWith(
                                    labelText:
                                        'Select a date for ${taskDetail.value}',
                                  ),
                                  onTap: () async {
                                    FocusScope.of(context).requestFocus(
                                        FocusNode()); // Dismiss keyboard
                                    DateTime? selectedDate =
                                        await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime(2000),
                                      lastDate: DateTime(2101),
                                    );
                                    if (selectedDate != null) {
                                      final formatted =
                                          "${selectedDate.toLocal()}"
                                              .split(' ')[0];
                                      controller.text = formatted;
                                      taskDetail.value = formatted;
                                      setState2(() {
                                        errors[taskDetail.title] = null;
                                      });
                                    }
                                  },
                                  readOnly: true,
                                ),
                              ],
                            );
                          case 'expirydate':
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextFormField(
                                  controller: controller,
                                  decoration: inpDecor().copyWith(
                                    labelText:
                                        'Select a date for ${taskDetail.title}',
                                    errorText: errorText,
                                  ),
                                  onTap: () async {
                                    FocusScope.of(context).requestFocus(
                                        FocusNode()); // Dismiss keyboard
                                    DateTime? selectedDate =
                                        await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime(2000),
                                      lastDate: DateTime(2101),
                                    );
                                    if (selectedDate != null) {
                                      final formatted =
                                          "${selectedDate.toLocal()}"
                                              .split(' ')[0];
                                      controller.text = formatted;
                                      taskDetail.value = selectedDate;
                                      setState2(() {
                                        errors[taskDetail.title] = null;
                                      });
                                    }
                                  },
                                  readOnly: true,
                                ),
                                const SizedBox(height: 10),
                              ],
                            );
                          default:
                            return const SizedBox();
                        }
                      }),
                      InkWell(
                        onTap: () async {
                          try {
                            final fileUrl = await ImagePickerService()
                                .pickFile(context, true);
                            if (fileUrl.isNotEmpty) {
                              selectedFile.addAll(fileUrl);
                            }
                            setState2(() {});
                          } catch (e) {
                            debugPrint("Failed to pick doc: $e");
                            setState2(() {
                              loading = false;
                            });
                          }
                        },
                        borderRadius: BorderRadius.circular(3),
                        child: Container(
                          padding: EdgeInsets.all(10),
                          width: double.maxFinite,
                          height: 150,
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade400),
                              borderRadius: BorderRadius.circular(7),
                              // color: const Color.fromARGB(9, 0, 0, 0),
                              color: const Color.fromRGBO(0, 0, 0, 0)),
                          child: (selectedFile.isEmpty)
                              ? const Icon(Icons.file_copy_outlined)
                              : Wrap(
                                  spacing: 10,
                                  runSpacing: 10,
                                  children: [
                                    ...List.generate(selectedFile.length,
                                        (index) {
                                      return selectedFile[index] == null
                                          ? const Center(
                                              child: Icon(
                                                  Icons.file_copy_outlined))
                                          : (selectedFile[index]?.type ==
                                                  'image')
                                              ? SizedBox(
                                                  height: 50,
                                                  width: 50,
                                                  child: Image.memory(
                                                    selectedFile[index]!
                                                        .uInt8List,
                                                    width: double.maxFinite,
                                                    fit: BoxFit.cover,
                                                  ),
                                                )
                                              : Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  spacing: 8,
                                                  children: [
                                                    const Icon(
                                                        Icons.picture_as_pdf),
                                                    Text(selectedFile[index]
                                                            ?.name ??
                                                        "")
                                                  ],
                                                );
                                    }),
                                    SizedBox(
                                        height: 50,
                                        width: 50,
                                        child: Icon(Icons.add,
                                            color: Colors.black))
                                  ],
                                ),
                        ),
                      ),

                      // InkWell(
                      //   onTap: () async {
                      //     try {
                      //       final fileUrl = await ImagePickerService().pickFile(
                      //         context,
                      //       );
                      //       if (fileUrl != null) {
                      //         selectedFile = fileUrl;
                      //       }
                      //       setState2(() {});
                      //     } catch (e) {
                      //       debugPrint("Failed to pick doc: $e");
                      //       setState2(() {
                      //         loading = false;
                      //       });
                      //     }
                      //   },
                      //   borderRadius: BorderRadius.circular(3),
                      //   child: Container(
                      //     width: double.maxFinite,
                      //     height: 150,
                      //     clipBehavior: Clip.antiAlias,
                      //     decoration: BoxDecoration(
                      //         border: Border.all(color: Colors.grey.shade400),
                      //         borderRadius: BorderRadius.circular(7),
                      //         // color: const Color.fromARGB(9, 0, 0, 0),
                      //         color: Colors.transparent),
                      //     child: selectedFile == null
                      //         ? const Icon(Icons.file_copy_outlined)
                      //         : selectedFile!.type == 'image'
                      //             ? Image.memory(
                      //                 selectedFile!.uInt8List,
                      //                 width: double.maxFinite,
                      //                 fit: BoxFit.cover,
                      //               )
                      //             : Column(
                      //                 crossAxisAlignment:
                      //                     CrossAxisAlignment.center,
                      //                 mainAxisAlignment:
                      //                     MainAxisAlignment.center,
                      //                 spacing: 8,
                      //                 children: [
                      //                   const Icon(Icons.picture_as_pdf),
                      //                   Text(selectedFile!.name)
                      //                 ],
                      //               ),
                      //   ),
                      // ),
                      // if (selectedFile == null)
                      //   Text(
                      //     "Please attach a file.",
                      //     style: TextStyle(color: Colors.red, fontSize: 12),
                      //   ),
                    ],
                  ),
                ),
                actions: loading
                    ? [
                        const Center(
                          child: SizedBox(
                            height: 24,
                            width: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2.5,
                            ),
                          ),
                        )
                      ]
                    : [
                        TextButton(
                          onPressed: () async {
                            bool hasError = false;

                            for (var detail in task.taskDetails) {
                              final value = (detail.type == "ExpiryDate")
                                  ? Timestamp.fromDate(detail.value)
                                  : controllers[detail.title]?.text.trim();
                              if (value == null) {
                                errors[detail.title] = 'This field is required';
                                hasError = true;
                              } else {
                                errors[detail.title] = null;
                                detail.value = value;
                              }
                            }
                            for (var element in selectedFile) {
                              if (element == null) {
                                hasError = true;
                                break;
                              }
                            }

                            setState2(() {});

                            if (hasError) return;

                            try {
                              setState2(() {
                                loading = true;
                              });
                              List<Map> files = [];
                              for (var element in selectedFile) {
                                final fileUrl = element != null
                                    ? await uploadUserDocs(element)
                                    : "";
                                files.add({element?.type: fileUrl});
                              }
                              // Upload file if selected
                              // final fileUrl = selectedFile != null
                              //     ? await uploadUserDocs(selectedFile!)
                              //     : "";
                              final uuid = Uuid();
                              final documentId = uuid.v4();
                              Map fielddataMap = {};
                              for (var task in task.taskDetails) {
                                fielddataMap.addEntries(task.toJson().entries);
                              }
                              final Map<String, dynamic> newTaskDoc = {
                                'documentId': documentId,
                                'docName': task.taskName,
                                // 'fileType': selectedFile != null
                                //     ? selectedFile!.type
                                //     : "",
                                // 'fileUrl': fileUrl,
                                "isDeleted": false,
                                "files": files,
                                'fieldData': fielddataMap,
                                'uId': FBAuth.auth.currentUser?.uid,
                                "vehicleDoc": false,
                                "isArchived": false,
                                "userName": _homeController.user?.name,
                                "userContact": _homeController.user?.number,
                              };

                              // Push the task doc to the selected vehicle document
                              // await FBFireStore.users
                              //     .doc(_homeController.user!.userId)
                              //     .update({
                              //   'documents':
                              //       FieldValue.arrayUnion([newTaskDoc]),
                              // });
                              await FBFireStore.usersDocs.add(newTaskDoc);

                              await addRecentToFirestore(task.taskName);

                              setState2(() {
                                loading = false;
                              });

                              if (context.mounted) {
                                Navigator.of(context).pop();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text("Document added"),
                                  ),
                                );
                              }
                            } catch (e) {
                              debugPrint("Error saving document: $e");
                              setState2(
                                () {
                                  loading = false;
                                },
                              );
                            }
                          },
                          child: const Text("Add"),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: const Text("Cancel"),
                        ),
                      ],
              );
            },
          );
        },
      );
    } catch (e) {
      debugPrint("Error loading task details: $e");
    }
  }

  Future<dynamic> addVehicleForm(
    BuildContext context,
    VehicleModel? vehicle,
    String userId,
  ) {
    bool loading = false;
    final formKey = GlobalKey<FormState>();

    TextEditingController vehicleNameController = TextEditingController();
    TextEditingController vehicleNumberController = TextEditingController();

    if (vehicle != null) {
      vehicleNameController.text = vehicle.vehicleName.toUpperCase();
      vehicleNumberController.text = vehicle.vehicleNumber.toUpperCase();
    }

    return showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState2) {
            return AlertDialog(
              contentPadding:
                  const EdgeInsets.symmetric(vertical: 15, horizontal: 25),
              title: Text(vehicle != null ? "Edit Vehicle" : "Add Vehicle"),
              content: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: vehicleNameController,
                      cursorHeight: 20,
                      decoration:
                          inpDecor().copyWith(labelText: 'Vehicle Name'),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter vehicle name';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 20),
                    TextFormField(
                      controller: vehicleNumberController,
                      cursorHeight: 20,
                      decoration:
                          inpDecor().copyWith(labelText: 'Vehicle Number'),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter vehicle number';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
              actions: loading
                  ? [
                      const Center(
                        child: SizedBox(
                          height: 25,
                          width: 25,
                          child: CircularProgressIndicator(strokeWidth: 2.5),
                        ),
                      )
                    ]
                  : [
                      TextButton(
                        onPressed: () async {
                          if (!formKey.currentState!.validate()) {
                            return;
                          }

                          try {
                            setState2(() {
                              loading = true;
                            });

                            final data = {
                              'userId': userId,
                              'vehicleName': vehicleNameController.text.trim(),
                              'chassisNumber': '',
                              'vehicleNumber':
                                  vehicleNumberController.text.trim(),
                              'vehicleType': '',
                              "isDeleted": false,
                              'createdAt': vehicle != null
                                  ? vehicle.createdAt
                                  : FieldValue.serverTimestamp(),
                            };

                            await addVehicle(
                                '${vehicleNameController.text.trim()} ${vehicleNumberController.text.trim()}',
                                data);

                            if (context.mounted) {
                              Navigator.of(context).pop();
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text("Vehicle Added"),
                                  duration: Duration(seconds: 2),
                                ),
                              );
                            }
                          } catch (e, stack) {
                            debugPrint("❌ Error in user add/update: $e");
                            debugPrint("🧵 Stacktrace: $stack");

                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                  content: Text("Something went wrong: $e")),
                            );
                          } finally {
                            setState2(() {
                              loading = false;
                            });
                          }
                        },
                        child: const Text("Add"),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text("Cancel"),
                      ),
                    ],
            );
          },
        );
      },
    );
  }

  void showImageDialog(BuildContext context, BannerModel banner) {
    showDialog(
      context: context,
      builder: (_) {
        bool isSharing = false;

        return ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: StatefulBuilder(
            builder: (context, setState) => Dialog(
              insetPadding: EdgeInsets.all(0),
              backgroundColor: Colors.white,
              child: Column(
                // fit: StackFit.expand,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Align(
                    alignment: Alignment.topRight,
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8, top: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        mainAxisSize: MainAxisSize.min,
                        spacing: 8,
                        children: [
                          isSharing
                              ? Padding(
                                  padding: const EdgeInsets.only(right: 12),
                                  child: SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: primaryColor,
                                    ),
                                  ),
                                )
                              : InkWell(
                                  onTap: () async {
                                    setState(() => isSharing = true);
                                    try {
                                      final uri = Uri.parse(banner.image);
                                      final response =
                                          await HttpClient().getUrl(uri);
                                      final imageBytes =
                                          await consolidateHttpClientResponseBytes(
                                              await response.close());
                                      // final dir = await getTemporaryDirectory();
                                      // final imagePath =
                                      //     '${dir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
                                      // final file = await File(imagePath)
                                      //     .writeAsBytes(imageBytes);

                                      await SharePlus.instance.share(
                                        ShareParams(
                                          title: banner.title,
                                          files: [
                                            XFile.fromData(imageBytes,
                                                mimeType: 'image/jpeg'),
                                          ],
                                        ),
                                      );
                                    } catch (e) {
                                      debugPrint("❌ Share error: $e");
                                    } finally {
                                      setState(() => isSharing = false);
                                    }
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(3),
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                            color: Colors.black26,
                                            blurRadius: 2,
                                            // spreadRadius: 0,
                                            spreadRadius: 1,
                                            offset: Offset(0, 2)),
                                      ],
                                    ),
                                    child: const Icon(
                                      Icons.share,
                                      size: 24,
                                      color: primaryColor,
                                    ),
                                  ),
                                ),
                          InkWell(
                            onTap: () => Navigator.of(context).pop(),
                            child: Container(
                              padding: const EdgeInsets.all(3),
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black26,
                                    blurRadius: 2,
                                    // spreadRadius: 0,
                                    spreadRadius: 1,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                CupertinoIcons.xmark,
                                size: 24,
                                color: primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 6),
                  Center(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: InteractiveViewer(
                        child: UniversalImage(
                          banner.image,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> addVehicle(String message, Map<String, dynamic> data) async {
    try {
      await FBFireStore.vehicles.add(data);
      await addRecentToFirestore(message);
    } catch (e, stack) {
      debugPrint("🔥 Critical Firestore Error: ${e.toString()}");
      debugPrint("Stack trace: $stack");
      rethrow;
    }
  }

  Future<void> addRecentToFirestore(
    String message,
  ) async {
    try {
      UserModel? user = await StorageService.getUserFromPrefs();
      DateTime now = DateTime.now();
      String formattedDate = DateFormat('dd/MM/yyyy - hh:mm').format(now);

      await FirebaseFirestore.instance.collection('recents').add({
        'userid': user?.userId,
        'name': user?.name,
        'email': user?.email,
        'number': user?.number,
        'message':
            'Data updated: $formattedDate - \n ${user?.name} - ${user?.number} - $message',
        'createdAt': FieldValue.serverTimestamp(),
      });
      debugPrint("✅ Recent contact added.");
    } catch (e) {
      debugPrint("❌ Failed to add recent: $e");
    }
  }
}

class VehicleCardMobile extends StatefulWidget {
  const VehicleCardMobile({
    super.key,
    required this.vehiclesList,
  });

  final VehicleModel vehiclesList;

  @override
  State<VehicleCardMobile> createState() => _VehicleCardMobileState();
}

class _VehicleCardMobileState extends State<VehicleCardMobile> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    loadVehicleDocs();
  }

  UserDocsModel? insuranceDoc;

  loadVehicleDocs() async {
    try {
      insuranceDoc = (await FBFireStore.usersDocs
              .where('vehicleDoc', isEqualTo: true)
              .where('vehicleId', isEqualTo: widget.vehiclesList.docId)
              .where('docName', isEqualTo: 'Insurance')
              .limit(1)
              .get())
          .docs
          .map((e) => UserDocsModel.fromSnapshot(e))
          .firstOrNull;
    } catch (e) {
      debugPrint("Error loading vehicle docs: $e");
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () =>
          Get.to(() => VehicleScreen(vehicleModel: widget.vehiclesList)),
      child: Padding(
        padding: const EdgeInsets.only(right: AppInsets.s16),
        child: Container(
          key: ValueKey(DateTime.now()),

          padding: const EdgeInsets.only(top: AppInsets.s10),
          // width: SizeConfig().screenWidth * 0.44,
          constraints: BoxConstraints(
            maxWidth: SizeConfig().screenWidth * 0.44,
            minWidth: SizeConfig().screenWidth * 0.44,
            minHeight: SizeConfig().screenWidth * 0.50,
            maxHeight: SizeConfig().screenWidth * 0.50,
          ),
          // decoration: BoxDecoration(
          //   color: Colors.white,
          //   borderRadius: BorderRadius.all(
          //       Radius.circular(AppInsets.s4)),
          //   border: Border.all(
          //     color: ColorConstants.borderColor,
          //     width: AppInsets.s1,
          //   ),
          // ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(AppInsets.s10)),
            boxShadow: [
              BoxShadow(
                  color: const Color.fromARGB(255, 230, 230, 230),
                  // spreadRadius: AppInsets.s36,
                  blurRadius: AppInsets.s12,
                  blurStyle: BlurStyle.outer)
            ],
            border: Border.all(
              color: const Color.fromARGB(255, 231, 231, 231),
              width: AppInsets.s1,
            ),
          ),
          child: Column(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(AppInsets.s6),
                width: AppInsets.s76,
                height: AppInsets.s76,
                decoration: BoxDecoration(
                  // color: ColorConstants.grayBackGroundColor,
                  borderRadius: BorderRadius.circular(AppInsets.s4),
                ),
                child: true
                    ? Image.asset(
                        'assets/images/car_blue.png',
                      )
                    : UniversalImage(
                        SvgsConstants.car,
                        height: AppInsets.s24,
                        width: AppInsets.s24,
                      ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                    vertical: AppInsets.s6, horizontal: AppInsets.pageMargin),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.vehiclesList.vehicleNumber.toUpperCase(),
                      style: TextStyle(
                        fontSize: AppInsets.s16,
                        color: ColorConstants.black,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppFonts.lexend,
                      ),
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      (insuranceDoc?.fieldData
                                  ?.firstWhereOrNull((field) =>
                                      field.title.toLowerCase() ==
                                      'expiry date')
                                  ?.value as Timestamp?)
                              ?.toDate()
                              .goodDayDate() ??
                          '-',
                      style: TextStyle(
                        fontSize: AppInsets.s12,
                        color: ((insuranceDoc?.fieldData
                                        ?.firstWhereOrNull((field) =>
                                            field.title.toLowerCase() ==
                                            'expiry date')
                                        ?.value as Timestamp?)
                                    ?.toDate()) ==
                                null
                            ? ColorConstants.black
                            : ((insuranceDoc?.fieldData
                                                ?.firstWhereOrNull((field) =>
                                                    field.title.toLowerCase() ==
                                                    'expiry date')
                                                ?.value as Timestamp?)
                                            ?.toDate())
                                        ?.compareTo(DateTime.now()) ==
                                    -1
                                ? Colors.red
                                : ColorConstants.black,
                        fontWeight: FontWeight.w500,
                        fontFamily: AppFonts.lexend,
                      ),
                      textAlign: TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // Text(
                    //   'Documents: ${vehiclesList[index].vehicleDocuments.length}',
                    //   style: TextStyle(
                    //     fontSize: AppInsets.s14,
                    //     color:
                    //         ColorConstants.textFieldHintColor,
                    //     fontWeight: FontWeight.w400,
                    //     fontFamily: AppFonts.lexend,
                    //   ),
                    //   textAlign: TextAlign.start,
                    //   overflow: TextOverflow.ellipsis,
                    // ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class BannerUI extends StatefulWidget {
  const BannerUI({super.key, required this.banner});
  final BannerModel banner;
  @override
  State<BannerUI> createState() => BannerUIState();
}

class BannerUIState extends State<BannerUI> {
  bool isSharing = false;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(builder: (_homeController) {
      return Padding(
        padding: EdgeInsets.symmetric(
          horizontal: AppInsets.pageMargin,
          vertical: AppInsets.s10,
        ),
        child: Stack(children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: UniversalImage(
              widget.banner.image,
              fit: BoxFit.cover,
              width: double.infinity,
            ),
          ),
          Align(
            alignment: Alignment.topRight,
            child: isSharing
                ? Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: CircularProgressIndicator(
                      strokeWidth: 3,
                      constraints: BoxConstraints(
                        maxWidth: 24,
                        maxHeight: 24,
                        minWidth: 24,
                        minHeight: 24,
                      ),
                    ),
                  )
                : IconButton(
                    onPressed: () async {
                      setState(() => isSharing = true);
                      if (widget.banner.link.isNotEmpty) {
                        bool result = await _homeController
                            .redirectUrl(widget.banner.link);
                        if (!result) {
                          await shareBanner(widget.banner);
                          (widget.banner);
                        }
                      } else {
                        await shareBanner(widget.banner);
                      }
                      setState(() => isSharing = false);
                    },
                    icon: Container(
                      height: 32,
                      width: 32,
                      decoration: BoxDecoration(
                          color: Colors.white, shape: BoxShape.circle),
                      child: Center(
                        child: Icon(
                          size: 24,
                          Icons.share,
                          color: Colors.black,
                        ),
                      ),
                    )),
          ),
        ]),
      );
    });
  }

  shareBanner(BannerModel banner) async {
    try {
      final uri = Uri.parse(banner.image);
      final response = await HttpClient().getUrl(uri);
      final imageBytes =
          await consolidateHttpClientResponseBytes(await response.close());
      // final dir = await getTemporaryDirectory();
      // final imagePath =
      //     '${dir.path}/${DateTime.now().millisecondsSinceEpoch}.jpg';
      // final file = await File(imagePath)
      //     .writeAsBytes(imageBytes);

      await SharePlus.instance.share(
        ShareParams(
          title: banner.title,
          files: [
            XFile.fromData(imageBytes, mimeType: 'image/jpeg'),
          ],
        ),
      );
    } catch (e) {
      debugPrint("❌ Share error: $e");
    } finally {}
  }
}
