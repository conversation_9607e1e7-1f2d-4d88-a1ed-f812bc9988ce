import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/shared/shared.dart';
import 'package:mass_ibs/shared/widgets/modal_progress_hud.dart';

import '../../shared/widgets/universal_image.dart';
import 'auth_controller.dart';

class LoginScreen extends GetView<AuthController> {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: SafeArea(
          child: Center(
            child: _buildItems(context),
          ),
        ),
      ),
    );
  }

  Widget _buildItems(BuildContext context) {
    return Obx(() {
      final isEnabled = controller.isEnabled.value;

      return ModalProgressHUD(
        inAsyncCall: controller.isActionInProgress.value,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppInsets.pageMargin),
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints:
                  BoxConstraints(minHeight: MediaQuery.sizeOf(context).height),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  SizedBox(height: SizeConfig().screenHeight * 0.1),
                  UniversalImage(
                    ImageConstants.appLogo,
                    width: SizeConfig().screenWidth * 0.40,
                  ),
                  SizedBox(height: AppInsets.s36),
                  Text(
                    'Enter your Number',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: AppInsets.s32,
                      fontWeight: FontWeight.bold,
                      fontFamily: AppFonts.lexend,
                    ),
                  ),
                  SizedBox(height: AppInsets.s4),
                  Text(
                    'We’ll send an SMS code for verification',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: AppInsets.s14,
                      color: ColorConstants.lightGray,
                      fontFamily: AppFonts.lexend,
                    ),
                  ),
                  SizedBox(height: AppInsets.s24),
                  AbsorbPointer(
                    absorbing: !isEnabled,
                    child: TextField(
                      controller: controller.loginMobileController,
                      enabled: controller.isEnabled.value,
                      keyboardType: TextInputType.phone,
                      style: TextStyle(
                        fontSize: AppInsets.s18,
                        fontWeight: FontWeight.w600,
                        fontFamily: AppFonts.lexend,
                        color: isEnabled
                            ? ColorConstants.black
                            : ColorConstants.textFieldHintColor,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(10),
                      ],
                      onChanged: (value) {
                        controller.mobileNumber.value = value;
                        controller.validateNumber(
                            value); // Validate on each keystroke
                      },
                      decoration: InputDecoration(
                        labelText: 'Mobile Number',
                        errorText:
                            controller.errorMessageMobileNumber.value.isEmpty
                                ? null
                                : controller.errorMessageMobileNumber.value,
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide: BorderSide(
                            color: ColorConstants.redColor,
                          ), // Default border
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide: BorderSide(
                            color: ColorConstants.redColor,
                          ), // Default border
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide:
                              BorderSide(color: ColorConstants.borderColor),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide:
                              BorderSide(color: ColorConstants.borderColor),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide: BorderSide(
                            color: ColorConstants.secondaryAppColor,
                            width: 2.0,
                          ),
                        ),
                        filled: true,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                        // Inner padding
                        hintText: "Mobile number",
                        hintStyle: TextStyle(
                          fontSize: AppInsets.s16,
                          color: ColorConstants.textFieldHintColor,
                          fontWeight: FontWeight.w600,
                          fontFamily: AppFonts.lexend,
                        ),
                      ),
                    ),
                  ),
                  if (controller.otpVisible.value) ...[
                    SizedBox(height: AppInsets.s24),
                    TextField(
                      controller: controller.otpController,
                      keyboardType: TextInputType.number,
                      onChanged: (value) {
                        controller.otpCode.value = value;
                        controller
                            .validateOtp(value); // Validate on each keystroke
                      },
                      style: TextStyle(
                        fontSize: AppInsets.s18,
                        fontWeight: FontWeight.w600,
                        fontFamily: AppFonts.lexend,
                        color: ColorConstants.black,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(6),
                      ],
                      decoration: InputDecoration(
                        labelText: 'OTP',
                        errorText: controller.errorMessageOTP.value.isEmpty
                            ? null
                            : controller.errorMessageOTP.value,
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide: BorderSide(
                            color: ColorConstants.redColor,
                          ), // Default border
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide: BorderSide(
                            color: ColorConstants.redColor,
                          ), // Default border
                        ),
                        disabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide: BorderSide(
                              color: ColorConstants.textFieldHintColor),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide: BorderSide(
                              color: ColorConstants.textFieldHintColor),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                          borderSide: BorderSide(
                            color: ColorConstants.secondaryAppColor,
                            width: 2.0,
                          ),
                        ),
                        filled: true,
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                        // Inner padding
                        hintText: "Enter code number",
                        hintStyle: TextStyle(
                          fontSize: AppInsets.s16,
                          color: ColorConstants.textFieldHintColor,
                          fontWeight: FontWeight.w600,
                          fontFamily: AppFonts.lexend,
                        ),
                      ),
                    ),
                    SizedBox(height: AppInsets.s16),
                  ],
                  if (controller.timerActive.value) ...[
                    Text(
                      'Resend code in ${controller.remainingSeconds}s',
                      style: TextStyle(
                        fontSize: AppInsets.s14,
                        color: ColorConstants.secondaryAppColor,
                        fontFamily: AppFonts.lexend,
                      ),
                    ),
                  ],
                  if (controller.canResend.value) ...[
                    TextButton(
                      onPressed: () {
                        controller.sendOTP(
                            controller.mobileNumber.trim(), context);
                      },
                      child: const Text(
                        'Resent OTP',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                  SizedBox(height: AppInsets.s24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.secondaryAppColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(AppInsets.s4),
                        ),
                        padding: EdgeInsets.symmetric(vertical: AppInsets.s16),
                        elevation: 0,
                      ),
                      onPressed: () {
                        if (controller.otpVisible.value) {
                          if (controller.errorMessageOTP.value.isEmpty &&
                              controller.otpCode.value.length == 6) {
                            controller.verifyOTP(controller.otpCode.trim(),
                                controller.mobileNumber.trim(), context);
                          } else {
                            controller.validateOtp(
                              controller.otpController.text.trim(),
                            );
                          }
                        } else {
                          if (controller
                                  .errorMessageMobileNumber.value.isEmpty &&
                              controller.mobileNumber.value.length == 10) {
                            controller.sendOTP(
                                controller.mobileNumber.trim(), context);
                          } else {
                            controller.validateNumber(
                              controller.loginMobileController.text.trim(),
                            );
                          }
                        }
                      },
                      child: Text(
                        'Continue',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: AppInsets.s16,
                          fontWeight: FontWeight.w600,
                          fontFamily: AppFonts.lexend,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
