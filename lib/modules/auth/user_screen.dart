import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../shared/constants/colors.dart';
import '../../shared/widgets/modal_progress_hud.dart';
import 'auth_controller.dart';

class UserScreen extends GetView<AuthController> {
  const UserScreen(
    this.mobileNumber,
    this.uId, {
    super.key,
  });

  final String mobileNumber;
  final String uId;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: SafeArea(
          child: Center(
            child: _buildItems(context),
          ),
        ),
      ),
    );
  }

  Widget _buildItems(BuildContext context) {
    return Obx(() {
      return ModalProgressHUD(
        inAsyncCall: controller.isActionInProgress.value,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppInsets.pageMargin),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              SizedBox(height: AppInsets.s36),
              Text(
                'Enter your Details',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: AppInsets.s32,
                  fontWeight: FontWeight.bold,
                  fontFamily: AppFonts.lexend,
                ),
              ),
              SizedBox(height: AppInsets.s48),
              TextField(
                controller: controller.nameController,
                keyboardType: TextInputType.text,
                style: TextStyle(
                  fontSize: AppInsets.s18,
                  fontWeight: FontWeight.w600,
                  fontFamily: AppFonts.lexend,
                ),
                onChanged: (value) {
                  controller.name.value = value;
                  controller.validateName(value); // Validate on each keystroke
                },
                decoration: InputDecoration(
                  labelText: 'Enter Your Name',
                  errorText: controller.errorMessageName.value.isEmpty
                      ? null
                      : controller.errorMessageName.value,
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide: BorderSide(
                      color: ColorConstants.redColor,
                    ), // Default border
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide: BorderSide(
                      color: ColorConstants.redColor,
                    ), // Default border
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide: BorderSide(color: ColorConstants.borderColor),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide: BorderSide(color: ColorConstants.borderColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide: BorderSide(
                      color: ColorConstants.secondaryAppColor,
                      width: 2.0,
                    ),
                  ),
                  filled: true,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                  // Inner padding
                  hintText: "Enter Your Name",
                  hintStyle: TextStyle(
                    fontSize: AppInsets.s16,
                    color: ColorConstants.textFieldHintColor,
                    fontWeight: FontWeight.w600,
                    fontFamily: AppFonts.lexend,
                  ),
                ),
              ),
              SizedBox(height: AppInsets.s24),
              TextField(
                controller: controller.emailController,
                keyboardType: TextInputType.emailAddress,
                onChanged: (value) {
                  controller.email.value = value;
                  controller.validateEmail(value); // Validate on each keystroke
                },
                style: TextStyle(
                  fontSize: AppInsets.s18,
                  fontWeight: FontWeight.w600,
                  fontFamily: AppFonts.lexend,
                  color: ColorConstants.black,
                ),
                decoration: InputDecoration(
                  labelText: 'Enter Your Email',
                  errorText: controller.errorMessageEmail.value.isEmpty
                      ? null
                      : controller.errorMessageEmail.value,
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide: BorderSide(
                      color: ColorConstants.redColor,
                    ), // Default border
                  ),
                  focusedErrorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide: BorderSide(
                      color: ColorConstants.redColor,
                    ), // Default border
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide:
                        BorderSide(color: ColorConstants.textFieldHintColor),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide:
                        BorderSide(color: ColorConstants.textFieldHintColor),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppInsets.s4),
                    borderSide: BorderSide(
                      color: ColorConstants.secondaryAppColor,
                      width: 2.0,
                    ),
                  ),
                  filled: true,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                  // Inner padding
                  hintText: "Enter Your Email",
                  hintStyle: TextStyle(
                    fontSize: AppInsets.s16,
                    color: ColorConstants.textFieldHintColor,
                    fontWeight: FontWeight.w600,
                    fontFamily: AppFonts.lexend,
                  ),
                ),
              ),
              SizedBox(height: AppInsets.s24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorConstants.secondaryAppColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppInsets.s4),
                    ),
                    padding: EdgeInsets.symmetric(vertical: AppInsets.s16),
                    elevation: 0,
                  ),
                  onPressed: () {
                    controller.validateName(
                      controller.nameController.text.trim(),
                    );

                    controller.validateEmail(
                      controller.emailController.text.trim(),
                    );

                    if (controller.errorMessageEmail.isEmpty &&
                        controller.errorMessageName.isEmpty) {
                      controller.saveUserData(
                        controller.name.trim(),
                        controller.email.trim(),
                        mobileNumber,
                        uId,
                      );
                    }
                  },
                  child: Text(
                    'Continue',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: AppInsets.s16,
                      fontWeight: FontWeight.w600,
                      fontFamily: AppFonts.lexend,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      );
    });
  }
}
