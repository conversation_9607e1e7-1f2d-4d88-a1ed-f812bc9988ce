import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mass_ibs/modules/auth/user_screen.dart';
import 'package:mass_ibs/modules/home/<USER>';
import 'package:mass_ibs/shared/firebase.dart';
import 'package:mass_ibs/shared/shared.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../web/models/user_model.dart';
import 'login_screen.dart';

class AuthController extends GetxController {
  AuthController();

  FirebaseAuth auth = FirebaseAuth.instance;
  var prefs = Get.find<SharedPreferences>();

  final loginMobileController = TextEditingController();
  final otpController = TextEditingController();
  final isActionInProgress = false.obs;
  final mobileNumber = ''.obs;
  final otpCode = ''.obs;
  final errorMessageMobileNumber = RxString('');
  final errorMessageOTP = RxString('');
  final isEnabled = true.obs;
  final otpVisible = false.obs;
  final canResend = false.obs;
  final remainingSeconds = 0.obs;
  final timerActive = false.obs;
  var verificationId = ''.obs;

  // User Details
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final errorMessageName = RxString('');
  final errorMessageEmail = RxString('');
  final email = ''.obs;
  final name = ''.obs;

  void toggleTFEnabled() => isEnabled.toggle();

  void startTimer() {
    if (timerActive.value) return;
    remainingSeconds.value = 30;
    timerActive.value = true;
    Future.doWhile(() async {
      await Future.delayed(Duration(seconds: 1));
      remainingSeconds.value--;
      if (remainingSeconds.value <= 0) {
        canResend.value = true;
        timerActive.value = false;
        return false;
      }
      return true;
    });
  }

  String? validateNumber(String? value) {
    if (value == null || value.isEmpty) {
      errorMessageMobileNumber.value = 'Please enter a number';
      return errorMessageMobileNumber.value;
    } else if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
      errorMessageMobileNumber.value = 'Only digits (0-9) allowed';
      return errorMessageMobileNumber.value;
    } else if (value.length != 10) {
      errorMessageMobileNumber.value = 'Must be exactly 10 digits';
      return errorMessageMobileNumber.value;
    }
    errorMessageMobileNumber.value = '';
    return null;
  }

  String? validateOtp(String? value) {
    if (value == null || value.isEmpty) {
      errorMessageOTP.value = 'Please enter a OTP';
      return errorMessageOTP.value;
    } else if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
      errorMessageOTP.value = 'Only digits (0-9) allowed';
      return errorMessageOTP.value;
    } else if (value.length != 6) {
      errorMessageOTP.value = 'Must be exactly 6 digits';
      return errorMessageOTP.value;
    }
    errorMessageOTP.value = '';
    return null;
  }

  String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      errorMessageName.value = 'Please enter a name';
      return errorMessageName.value;
    }
    errorMessageName.value = '';
    return null;
  }

  String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      errorMessageEmail.value = 'Please enter a email';
      return errorMessageEmail.value;
    } else if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(value)) {
      errorMessageEmail.value = 'Enter Valid Email';
      return errorMessageEmail.value;
    }
    errorMessageEmail.value = '';
    return null;
  }

  void sendOTP(String phoneNumber, context) async {
    canResend(false);
    AppFocus.unFocus(context);
    isActionInProgress(true);
    if (!phoneNumber.startsWith('+')) {
      phoneNumber = '+91$phoneNumber';
    }
    debugPrint('M O B I L E $phoneNumber');
    await auth.verifyPhoneNumber(
      phoneNumber: phoneNumber,
      verificationCompleted: (PhoneAuthCredential credential) async {
        otpVisible(true);
        isActionInProgress(false);
      },
      verificationFailed: (FirebaseAuthException e) {
        otpVisible(false);
        isActionInProgress(false);
        Get.snackbar(
          "Error",
          e.message ?? "Verification failed",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.secondaryAppColor,
          colorText: Colors.white,
          margin: EdgeInsets.all(AppInsets.s16),
          borderRadius: AppInsets.s4,
          icon: Icon(Icons.check_circle, color: Colors.greenAccent),
          duration: Duration(seconds: 3),
          isDismissible: true,
        );
      },
      codeSent: (String verId, int? resendToken) {
        verificationId.value = verId;
        otpVisible(true);
        isActionInProgress(false);
        startTimer();
        Get.snackbar(
          "OTP Sent",
          "Check your phone for the OTP",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: ColorConstants.secondaryAppColor,
          colorText: Colors.white,
          margin: EdgeInsets.all(AppInsets.s16),
          borderRadius: AppInsets.s4,
          icon: Icon(Icons.check_circle, color: Colors.greenAccent),
          duration: Duration(seconds: 3),
          isDismissible: true,
        );
      },
      codeAutoRetrievalTimeout: (String verId) {
        isActionInProgress(false);
        verificationId.value = verId;
      },
    );
  }

  void verifyOTP(String otp, String phoneNumber, context) async {
    AppFocus.unFocus(context);
    isActionInProgress(true);
    try {
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: verificationId.value,
        smsCode: otp,
      );

      UserCredential userCredential =
          await auth.signInWithCredential(credential);

      if (!phoneNumber.startsWith('+')) {
        phoneNumber = '+91$phoneNumber';
      }

      await fetchAndSaveUserByPhone(
          phoneNumber, userCredential.user!.uid.toString());
    } catch (e, stack) {
      debugPrint(e.toString());
      debugPrint(stack.toString());
      isActionInProgress(false);
      Get.snackbar(
        "Failed",
        "Invalid OTP or verification failed",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: ColorConstants.secondaryAppColor,
        colorText: Colors.white,
        margin: EdgeInsets.all(AppInsets.s16),
        borderRadius: AppInsets.s4,
        icon: Icon(Icons.check_circle, color: Colors.greenAccent),
        duration: Duration(seconds: 3),
        isDismissible: true,
      );
    }
  }

  Future<void> fetchAndSaveUserByPhone(String phoneNumber, String uId) async {
    try {
      // 1. Query Firestore
      final query = await FBFireStore.users
          .where('number', isEqualTo: phoneNumber)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        final user = UserModel.fromDocSnap(query.docs.first);

        // 2. Convert to JSON string
        final userJson = jsonEncode(user.toJson());

        // 3. Save to SharedPreferences
        await prefs.setString(StorageConstants.userInfo, userJson);

        String jsonString = jsonEncode(user.toJson());
        debugPrint(jsonString);

        debugPrint('User data saved to preferences');
        // 👇 Navigate to Home Page
        isActionInProgress(false);
        Get.offAll(() => HomeScreen());
      } else {
        isActionInProgress(false);
        Get.offAll(() => UserScreen(phoneNumber, uId));
        debugPrint('No user found for phone number: $phoneNumber');
      }
    } catch (e) {
      debugPrint('Error fetching user: $e');
    }
  }

  Future<Map<String, dynamic>> saveUserData(
    String name,
    String email,
    String mobileNumber,
    String uId,
  ) async {
    final FirebaseFirestore db = FirebaseFirestore.instance;

    try {
      isActionInProgress(true);

      User? user = auth.currentUser;

      if (user == null) {
        return {'success': false, 'msg': 'No authenticated user found'};
      }

      final data = {
        'userid': uId,
        'name': name,
        'number': mobileNumber,
        'email': email,
        'firebaseToken': "",
        'createdAt': FieldValue.serverTimestamp(),
        'documents': [],
      };

      await db.collection('users').doc(user.uid).set(data);

      debugPrint('User data saved successfully in Firestore');

      // 1. Query Firestore
      final query = await FBFireStore.users
          .where('number', isEqualTo: mobileNumber)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        final user = UserModel.fromDocSnap(query.docs.first);

        // 2. Convert to JSON string
        final userJson = jsonEncode(user.toJson());

        // 3. Save to SharedPreferences
        await prefs.setString(StorageConstants.userInfo, userJson);

        String jsonString = jsonEncode(user.toJson());
        debugPrint(jsonString);

        debugPrint('User data saved to preferences');

        await addRecentToFirestore(
          user.userId,
          user.name,
          user.email,
          user.number,
        );

        // 👇 Navigate to Home Page
        isActionInProgress(false);

        Get.offAll(() => HomeScreen());
      }
      return {'success': true, 'msg': 'User data saved successfully'};
    } catch (error) {
      debugPrint(
          "Failed to save user data to Firestore, deleting the user! $error");
      try {
        User? user = auth.currentUser;
        if (user != null) {
          await user.delete();
          debugPrint("User deleted successfully!");
        }
        isActionInProgress(false);

        Get.offAll(() => LoginScreen());
      } catch (deleteError) {
        isActionInProgress(false);

        Get.offAll(() => LoginScreen());
        debugPrint("Error deleting user: $deleteError");
      }
      isActionInProgress(false);

      return {'success': false, 'msg': error.toString()};
    }
  }

  Future<void> addRecentToFirestore(
    String userId,
    String name,
    String email,
    String number,
  ) async {
    try {
      await FirebaseFirestore.instance.collection('recents').add({
        'userid': userId,
        'name': name,
        'email': email,
        'number': number,
        'message': 'New user registered: $name : $number',
        'createdAt': FieldValue.serverTimestamp(),
      });
      print("✅ Recent contact added.");
    } catch (e) {
      print("❌ Failed to add recent: $e");
    }
  }

  @override
  void onClose() {
    super.onClose();
    emailController.clear();
    nameController.clear();
    otpController.clear();
    loginMobileController.clear();
    emailController.dispose();
    nameController.dispose();
    otpController.dispose();
    loginMobileController.dispose();
  }
}
