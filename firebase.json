{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"]}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "storage": {"rules": "storage.rules"}, "emulators": {"functions": {"port": 5020}, "firestore": {"port": 8080}, "pubsub": {"port": 8085}, "ui": {"enabled": true}, "singleProjectMode": true}}