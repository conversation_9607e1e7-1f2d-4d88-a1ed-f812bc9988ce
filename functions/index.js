
const { onCall } = require("firebase-functions/v2/https");
const { onRequest } = require("firebase-functions/v2/https");
const functions = require("firebase-functions");
const admin = require("firebase-admin");
const { FieldValue, Timestamp, Filter } = require("firebase-admin/firestore");
const testMode = false;
const unirest = require('unirest');
const { onSchedule } = require('firebase-functions/v2/scheduler');

const {
  onDocumentWritten, onDocumentCreated,
  // onDocumentCreated,
  onDocumentUpdated,
  // onDocumentDeleted,
  // Change,
  // FirestoreEvent
} = require("firebase-functions/v2/firestore");
admin.initializeApp();
const db = admin.firestore();
const auth = admin.auth();
const messaging = admin.messaging();

exports.updateUser = onCall(async (request) => {
  try {
    console.log("IN UPDATE USER");
    console.log(request.data);
    auth.updateUser(request.data.uid, {
      phoneNumber: request.data.phoneNumber,
      //email: request.data.email,
    });
    await admin.firestore().collection('users').doc(request.data.uid).update({
      'name': request.data.name,
      'number': request.data.phoneNumber,
      'email': request.data.email,
    });
    return { "success": true };
  } catch (error) {
    console.log(error);
    return { "success": false };
  }
});

exports.createUser = onCall(async (request) => {
  try {
    console.log("IN CREATE USER");

    // Ensure the user is authenticated
    if (!request.auth) return { status: 'error', code: 401, message: 'Not signed in' };

    // Step 1: Create a new user in Firebase Authentication
    const response1 = await auth.createUser({
      phoneNumber: request.data.phoneNumber,
      //email: request.data.email,
    });

    console.log('Successfully created new user:', response1.uid);
    console.log('phoneNumber', request.data.phoneNumber);
    console.log('Name:', request.data.name);
    console.log('Email:', request.data.email);
    console.log('createdAt:', FieldValue.serverTimestamp());

    // Step 2: Create the data object using the provided structure
    const data = {
      'userid': response1.uid,  // Using UID generated by Firebase Auth
      'name': request.data.name,
      'number': request.data.phoneNumber,
      'email': request.data.email,
      'firebaseToken': "",
      'createdAt': FieldValue.serverTimestamp(),
      'documents': [],
    };

    // Step 3: Save user data in the Firestore 'Users' collection
    try {
      let response2 = await db.collection('users').doc(response1.uid).set(data);

      console.log('User data saved successfully in Firestore');
      return { success: true, msg: response2 };
    } catch (error) {
      console.log("Failed to save user data to Firestore, deleting the user!", error);

      // If Firestore save fails, delete the user created in Firebase Auth
      await auth.deleteUser(response1.uid);
      console.log("User deleted successfully!");

      return { success: false, msg: error.message };
    }
  } catch (error) {
    console.log('Error creating new user:', error);
    return { success: false, msg: error.message, code: error.code };
  }
});

exports.deleteUser = onCall(async (request) => {
  try {
    console.log(request.data);
    try {
      await admin.auth().deleteUser(request.data.uid);
    } catch (error) {
      console.log(`Error deleting ${request.data.uid}`);
    }
    await admin.firestore().collection('users').doc(request.data.uid).delete().then((val) => {
      console.log(`Deleted ${request.data.uid}`);
      return { "success": true };
    });
  } catch (error) {
    console.log(error);
    return { "success": false };
  }
});

exports.notifyUser = onCall(async (request) => {
  sendNotificationToUser(request.body['title'], request.body['msg'], request.body['uid']);
});

exports.createCustomToken = onCall(async (request) => {
  return admin.auth().createCustomToken(request.body['uid']);
});



async function sendNotificationToUser(title, message, uid) {
  try {
    var user = await admin.firestore().collection("users")
      .doc(uid)
      .get();
    if (user.exists) {
      const payload = {
        tokens: user.data()['tokens'],
        notification: {
          title: title,
          body: message
        },
        android: {
          notification: {
            channel_id: "MassIbs"
          }
        },
        data: {}
      };
      messaging.sendEachForMulticast(payload).then((response) => {
        // Response is a message ID string.
        console.log('Successfully sent message:', response);
        return { success: true };
      }).catch((error) => {
        return { error: error.code };
      });
    }
  } catch (error) {
    console.log(error);
  }
}

exports.notifies = onDocumentWritten('news/{newsId}', async (event) => {
  try {
    const data = event.data.after.data();
    console.log(data.test == true ? "Test News Notification..." : "Global News Notification...");
    console.log("data.topic");
    console.log(data.topic);
    const payload = {
      topic: data.test === true ? 'test' : 'global',
      notification: {
        title: 'News update!!',
        body: data.title
      },
      android: {
        priority: "high",
        notification: {
          channel_id: "massIBS"
        }
      },
      apns: {
        headers: {
          'apns-priority': "10",
        },
        payload: {
          aps: {
            alert: {
              title: 'News update!!',
              body: data.title
            },
            sound: "default",
          }
        }
      },
      data: {
        title: data.title,
        body: data.desc
      }
    };
    messaging.send(payload).then((response) => {
      // Response is a message ID string.
      console.log('Successfully sent message:', response);
      return { success: true };
    }).catch((error) => {
      console.log('Error:', error.code);
      return { error: error.code };
    });

  } catch (error) {
    console.log(error);
  }
});



exports.notifies = onDocumentWritten('notifies/{userId}', async (event) => {
  try {
    const data = event.data.after.data();
    console.log(data.test == true ? "Test Notification..." : "Global Notification...");
    console.log("data.topic");
    console.log(data.topic);
    const payload = {
      topic: data.test === true ? 'test' : 'global',
      notification: {
        title: data.title,
        body: data.desc
      },
      android: {
        priority: "high",
        notification: {
          channel_id: "massIBS"
        }
      },
      apns: {
        headers: {
          'apns-priority': "10",
        },
        payload: {
          aps: {
            alert: {
              title: data.title,
              body: data.desc,
            },
            sound: "default",
          }
        }
      },
      data: {
        title: data.title,
        body: data.desc
      }
    };
    messaging.send(payload).then((response) => {
      // Response is a message ID string.
      console.log('Successfully sent message:', response);
      return { success: true };
    }).catch((error) => {
      console.log('Error:', error.code);
      return { error: error.code };
    });

  } catch (error) {
    console.log(error);
  }
});

// Shared notification function
async function sendAllNotifications(data) {
  try {
    const notificationData = {
      title: `Document Expiry Alert: ${data.docName}`,
      desc: `Your document ${data.docName} for ${data.entityName} expires on ${data.formattedDate}`,
      phone: data.phone,
      test: false // Set to true for testing
    };

    // Create a notification document to trigger the notifies function
    const db = admin.firestore();
    await db.collection('notifies').add(notificationData);

    return { success: true };
  } catch (error) {
    console.error('Notification error:', error);
    throw error;
  }
}

// Sends an SMS using Fast2SMS
function sendSMS(number, text, name, docName, vehicleNo, date) {



  return new Promise((resolve, reject) => {
    // var unirest = require('unirest');
    const url = date != null ? `https://www.fast2sms.com/dev/bulkV2?authorization=oE0rKPFUBaSWrKbbOxfmnywsaIF8GT046zavez6diyiSCgE6BWHqPHVLza89&route=dlt&sender_id=MASSBS&message=${text}&variables_values=${name}|${docName}|${vehicleNo}|${date}&numbers=${number.replace(/\D/g, '').slice(-10)}` : `https://www.fast2sms.com/dev/bulkV2?authorization=oE0rKPFUBaSWrKbbOxfmnywsaIF8GT046zavez6diyiSCgE6BWHqPHVLza89&route=dlt&sender_id=MASSBS&message=${text}&variables_values=${name}|${docName}|${vehicleNo}&numbers=${number.replace(/\D/g, '').slice(-10)}`;
    var req = unirest('GET', url)
      .end(function (res) {
        if (res.error) throw new Error(res.error);
        console.log(res.raw_body);
        console.log("SMS Response:", res.body);
        resolve(res.body);
      });

    resolve(req);



  });
}

// function sendSMS(number, text) {
//   const cleanNumber = number.replace(/\D/g, '').slice(-10);
//   const smsData = {
//     message: text,
//     language: "english",
//     route: "q",
//     numbers: cleanNumber,
//   };

//   console.log("Sending SMS to:", cleanNumber);
//   console.log("SMS Payload:", smsData);

//   return new Promise((resolve, reject) => {
//     const req = unirest("POST", "https://www.fast2sms.com/dev/bulkV2");

//     req.headers({ "authorization": "VdRsXWlpzOoSjnP23QvF5DIuNGq98hZTYxCkwL40BEMfryeaAKk4QRMwpEeHVUicfxr8FAg3zBsnmD0y" }); // Replace with actual key

//     req.form(smsData);

//     req.end(res => {
//       if (res.error) {
//         console.error("SMS Error:", res.error);
//         reject(res.error);
//       } else {
//         console.log("SMS Response:", res.body);
//         resolve(res.body);
//       }
//     });
//   });
// }

// Sends WhatsApp message via SquareFlow
function sendWSMessage(data) {



  return new Promise((resolve, reject) => {
    // var unirest = require('unirest');
    const url = data.fileUrl ? `https://app.squareflow.in/api/sendtemplate.php?LicenseNumber=${data.LicenseNumber}&APIKey=${data.APIKey}&Contact=${data.contact}&Template=${data.template}&Param=${data.param}&Fileurl=${data.fileUrl}&PDFName=${data.pdfName}` : `https://app.squareflow.in/api/sendtemplate.php?LicenseNumber=${data.LicenseNumber}&APIKey=${data.APIKey}&Contact=${data.contact}&Template=${data.template}&Param=${data.param}&PDFName=${data.pdfName}`;
    var req = unirest('GET', url)
      .end(function (res) {
        if (res.error) throw new Error(res.error);
        console.log(res.raw_body);
      });

    resolve(req);
  });

}

// exports.testFunc = onRequest(async (request, res) => {
//   try {
//     const smsText = `Dear {userName}, new {docName} has been successfully uploaded for Vehicle No. {vehicleNum}. For any queries or support, call ********** MASS INSURANCE`;
//     try {
//       await sendSMS('917016229116', '189814', { v1: userName, v2: docName, v3: vehicleNum });
//     } catch (err) {
//       console.error("onUserDocumentUpdate : SMS sending error:", err);
//     }
//     return res.send({ message: 'SMS sent successfully' });
//   } catch (error) {
//     console.error('Test function error:', error);
//     throw new functions.https.HttpsError('internal', 'Internal error');
//   }
// });

exports.onVehicleDataDocCreated = onDocumentCreated('usersDocs/{docId}', async (event) => {
  console.log('entered onUserDocumentUpdate');
  const doc = event.data.data();
  const isVehicle = doc.vehicleDoc || false;

  if ((doc.docName === 'Vehicle Fitness' || doc.docName === 'Insurance' || doc.docName === 'Vehicle Permit') && isVehicle) {

    const userName = doc.userName || 'User';
    const vehicleNum = doc.vehicleNum || '';
    // const phone = doc.userContact.replace('+', '');
    const phone = doc.userContact ? doc.userContact.replace('+', '') : null;



    if (!phone) return; // skip users with no phone

    // for (const doc of documents) {
    const docName = doc.docName || 'Unnamed Document';
    // const fieldData = doc.fieldData || [];



    console.log("Detected new vehicle document:", doc);


    var fileData = null;

    // if (Array.isArray(doc.files) && doc.files.length > 0) {
    //   // Extract URLs from each file object
    //   fileData = doc.files.map(fileObj => Object.values(fileObj)[0]);
    // } else {
    //   fileData = [];
    // }

    if (doc.files.length > 0) {
      const firstObj = doc.files[0]; // get the first object

      // Get the values of the first object as an array
      const values = Object.values(firstObj);

      if (values.length > 0) {
        fileData = values[0];
        console.log('First value:', fileData);
      } else {
        console.log('First object has no values');
        fileData = null;
      }
    } else {
      fileData = null;
    }

    if (fileData == null) {
      console.log('empty file');

      console.log(`name-${userName}`);
      console.log(`docname-${docName}`);
      console.log(`vehiclenu-${vehicleNum}`);
      console.log(`ph-${phone}`);


      const smsText = `189814`;
      // const smsText = `Dear ${userName}, new ${docName} has been successfully uploaded for Vehicle No. ${vehicleNum}. For any queries or support, call ********** MASS INSURANCE`;

      const wsData = {
        LicenseNumber: '84209111524',
        APIKey: 'Fy5kXjzh4IiSeaH7WGgw0rQlA',
        contact: phone,
        template: "new_doc",
        // param: `${userName}`,
        param: `${userName},${docName},${vehicleNum}`,
        pdfName: docName
      };



      try {
        await sendWSMessage(wsData);
      } catch (err) {
        console.error("onUserDocumentUpdate : WhatsApp sending error:", err);
      }
      // try {
      //   await sendSMS(phone, smsText, userName, docName, vehicleNum);
      // } catch (err) {
      //   console.error("onUserDocumentUpdate : SMS sending error:", err);
      // }
      return;
    }

    console.log(`data-${fileData}`);
    console.log(`name-${userName}`);
    console.log(`docname-${docName}`);
    // console.log(`vehiclenu-${vehicleNum}`);
    console.log(`ph-${phone}`);


    const smsText = '189814';
    // const smsText = `Hi ${userName}, a new document "${docName}" was uploaded for ${vehicleNum}.`;
    var publicUrl = convertDownloadUrlToPublicUrl(fileData);

    // var publicUrl = convertDownloadUrlToPublicUrl('https://firebasestorage.googleapis.com/v0/b/massibs.firebasestorage.app/o/UserDocs%2F1748341198442.jpg?alt=media&token=3e9765ac-8215-46e2-b67b-fb5053379fb6');
    // console.log(publicUrl);
    const wsData = {
      LicenseNumber: '84209111524',
      APIKey: 'Fy5kXjzh4IiSeaH7WGgw0rQlA',
      contact: phone,
      template: "verify_download_v2",
      param: `${userName}`,
      // param: `${userName},${docName},${vehicleNum}`,
      fileUrl: publicUrl,
      // fileUrl: '',
      //  fileUrl:"https://datapartner.btpr.online/ProductPictures/84209111524_Insurance.pdf",
      // fileUrl: "https://storage.googleapis.com/massibs.firebasestorage.app/UserDocs%2F1749190028828.jpg",
      pdfName: docName
    };



    try {
      await sendWSMessage(wsData);
    } catch (err) {
      console.error("onUserDocumentUpdate : WhatsApp sending error:", err);
    }
    // try {
    //   // await sendSMS('917016229116', smsText);
    //   await sendSMS(phone, smsText, userName, docName, vehicleNum);

    // } catch (err) {
    //   console.error("onUserDocumentUpdate : SMS sending error:", err);
    // }


  }
});

// exports.onVehicleDocumentUpdate = onDocumentUpdated('vehicles/{vehicleId}', async (event) => {
//   const before = event.data.before.data();
//   const after = event.data.after.data();

//   const beforeDocs = before.vehicleDocuments || [];
//   const afterDocs = after.vehicleDocuments || [];

//   const newDocs = afterDocs.filter(
//     doc => !beforeDocs.some(b => b.documentId === doc.documentId)
//   );

//   const phone = after.phone;
//   const ownerName = after.ownerName;
//   const vehicleName = after.vehicleName || "";

//   for (const doc of newDocs) {
//     console.log("Detected new vehicle document:", doc);

//     const smsText = `Hi ${ownerName}, new document "${doc.docName}" was uploaded for ${vehicleName}.`;

//     const wsData = {
//       contact: phone,
//       template: doc.template || "DEFAULT_VEHICLE_TEMPLATE",
//       param: doc.param || `${ownerName},${doc.docName},${doc.fileType}`,
//       fileUrl: doc.fileUrl || '',
//       urlParam: doc.urlParam || '',
//       headUrl: doc.headUrl || '',
//       headParam: doc.headParam || '',
//       name: ownerName,
//       pdfName: doc.docName
//     };

//    try {
//           await sendWSMessage(wsData);
//         } catch (err) {
//           console.error("onUserDocumentUpdate : WhatsApp sending error:", err);
//         }
//          try {
//            await sendSMS(phone, smsText);
//          } catch (err) {
//               console.error("onUserDocumentUpdate : SMS sending error:", err);
//          }
//   }
// });

// exports.notifyVehicleDocumentExpiries = onSchedule({
//   schedule: '0 0 * * *',
//   timeZone: 'Asia/Kolkata',
//   maxInstances: 1
// }, async (event) => {
//   const now = new Date();
//   const oneMonthLater = new Date();
//   oneMonthLater.setDate(now.getDate() + 30);

//   const vehiclesSnapshot = await db.collection('vehicles').get();

//   for (const vehicleDoc of vehiclesSnapshot.docs) {
//     const vehicleData = vehicleDoc.data();
//     const vehicleName = vehicleData.vehicleName || 'Unnamed Vehicle';
//     const vehicleDocuments = vehicleData.vehicleDocuments || [];
//     const phone = vehicleData.phone;
//     const ownerName = vehicleData.ownerName || 'There';

//     if (!phone) continue;

//     for (const doc of vehicleDocuments) {
//       const docName = doc.docName || 'Unnamed Document';
//       const fieldData = doc.fieldData || [];

//       for (const field of fieldData) {
//         const key = (field.fieldKey || '').toLowerCase();
//         const value = field.value;

//         // If field is 'expirydate' and value is a valid date string
//         if (key === 'expiry date' && typeof value === 'string') {
//           const expiryDate = new Date(value);
//           if (isNaN(expiryDate)) continue; // skip invalid date

//           if (expiryDate > now && expiryDate <= oneMonthLater) {
//             const formattedDate = expiryDate.toDateString();

//             // Send SMS
//             const smsText = `Hi ${ownerName}, the document "${docName}" for your vehicle "${vehicleName}" is expiring on ${formattedDate}.`;

//             try {
//               await sendSMS(phone, smsText);
//             } catch (smsErr) {
//               console.error("Error sending SMS:", smsErr);
//             }

//             // Send WhatsApp message
//             const wsData = {
//               contact: phone,
//               template: doc.template || "DOCUMENT_EXPIRY_ALERT",
//               param: doc.param || `${ownerName},${docName},${formattedDate}`,
//               fileUrl: doc.fileUrl || '',
//               urlParam: doc.urlParam || '',
//               headUrl: doc.headUrl || '',
//               headParam: doc.headParam || '',
//               name: ownerName,
//               pdfName: docName
//             };

//             try {
//               await sendWSMessage(wsData);
//             } catch (wsErr) {
//               console.error("Error sending WhatsApp message:", wsErr);
//             }
//           }
//         }
//       }
//     }
//   }

//   return null;
// });


function convertDownloadUrlToPublicUrl(downloadUrl) {
  try {
    const url = new URL(downloadUrl);
    const segments = url.pathname.split('/');
    // Find bucket name (segment after 'b')
    const bIndex = segments.indexOf('b');
    if (bIndex === -1 || bIndex + 1 >= segments.length) {
      throw new Error('Invalid download URL format: bucket not found');
    }
    const bucketName = segments[bIndex + 1];
    // Find file path (segment after 'o')
    const oIndex = segments.indexOf('o');
    if (oIndex === -1 || oIndex + 1 >= segments.length) {
      throw new Error('Invalid download URL format: file path not found');
    }
    const encodedFilePath = segments[oIndex + 1];
    // Decode file path (replace %2F with /)
    const filePath = decodeURIComponent(encodedFilePath);
    // Construct public URL
    const publicUrl = `https://storage.googleapis.com/${bucketName}/${filePath}`;
    return publicUrl;
  } catch (error) {
    console.error(error);
    return null;
  }
}
exports.notifyUserDocumentExpiries = onSchedule({
  schedule: '0 0 * * *',
  timeZone: 'Asia/Kolkata',
  maxInstances: 1,
  timeoutSeconds: 540,
}, async (event) => {
  // exports.testers = onRequest(async (request, response) => {

  try {
    const checkDate = new Date();
    const midnightToday = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate(),
      0, 0, 0, 0
    );
    const minus7DaysMidnight = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() - 7,
      0, 0, 0, 0
    ); const minus7DaysMidnightEnd = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() - 6,
      0, 0, 0, 0
    );

    const minus14DaysMidnight = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() - 14,
      0, 0, 0, 0
    ); const minus14DaysMidnightEnd = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() - 13,
      0, 0, 0, 0
    );

    const minus21DaysMidnight = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() - 21,
      0, 0, 0, 0
    ); const minus21DaysMidnightEnd = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() - 20,
      0, 0, 0, 0
    );
    const minus28DaysMidnight = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() - 28,
      0, 0, 0, 0
    ); const minus28DaysMidnightEnd = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() - 27,
      0, 0, 0, 0
    );
    const midnightTimestamp = Timestamp.fromDate(midnightToday);
    const midnightTimestamp1 = Timestamp.fromDate(minus7DaysMidnight);
    const midnightTimestamp1End = Timestamp.fromDate(minus7DaysMidnightEnd);
    const midnightTimestamp2 = Timestamp.fromDate(minus14DaysMidnight);
    const midnightTimestamp2End = Timestamp.fromDate(minus14DaysMidnightEnd);
    const midnightTimestamp3 = Timestamp.fromDate(minus21DaysMidnight);
    const midnightTimestamp3End = Timestamp.fromDate(minus21DaysMidnightEnd);
    const midnightTimestamp4 = Timestamp.fromDate(minus28DaysMidnight);
    const midnightTimestamp4End = Timestamp.fromDate(minus28DaysMidnightEnd);
    const querySnapshot1 = await db.collection('usersDocs')
      .where('vehicleDoc', '==', true)
      .where('isArchived', '==', false)
      .where('docName', 'in', ['Vehicle Fitness', 'Insurance', 'Vehicle Permit'])
      .where('fieldData.Expiry_Date.value', '>=', midnightTimestamp)
      .where('fieldData.Expiry_Date.value', '<', midnightTimestamp)
      .get(); const querySnapshot2 = await db.collection('usersDocs')
        .where('vehicleDoc', '==', true)
        .where('isArchived', '==', false)
        .where('docName', 'in', ['Vehicle Fitness', 'Insurance', 'Vehicle Permit'])
        .where('fieldData.Expiry_Date.value', '>=', midnightTimestamp1)
        .where('fieldData.Expiry_Date.value', '<', midnightTimestamp1End)
        .get();
    const querySnapshot3 = await db.collection('usersDocs')
      .where('vehicleDoc', '==', true)
      .where('isArchived', '==', false)
      .where('docName', 'in', ['Vehicle Fitness', 'Insurance', 'Vehicle Permit'])
      .where('fieldData.Expiry_Date.value', '>=', midnightTimestamp2)
      .where('fieldData.Expiry_Date.value', '<', midnightTimestamp2End)
      .get(); const querySnapshot4 = await db.collection('usersDocs')
        .where('vehicleDoc', '==', true)
        .where('isArchived', '==', false)
        .where('docName', 'in', ['Vehicle Fitness', 'Insurance', 'Vehicle Permit'])
        .where('fieldData.Expiry_Date.value', '>=', midnightTimestamp3)
        .where('fieldData.Expiry_Date.value', '<', midnightTimestamp3End)
        .get(); const querySnapshot5 = await db.collection('usersDocs')
          .where('vehicleDoc', '==', true)
          .where('isArchived', '==', false)
          .where('docName', 'in', ['Vehicle Fitness', 'Insurance', 'Vehicle Permit'])
          .where('fieldData.Expiry_Date.value', '>=', midnightTimestamp4)
          .where('fieldData.Expiry_Date.value', '<', midnightTimestamp4End)
          .get();

    console.log(`number of docs  ${querySnapshot3.docs.length}`);
    console.log(`number of docs  ${querySnapshot4.docs.length}`);
    console.log(`number of docs  ${querySnapshot5.docs.length}`);
    const querySnapshot = [
      ...querySnapshot1.docs, ...querySnapshot2.docs,
      ...querySnapshot3.docs, ...querySnapshot4.docs, ...querySnapshot5.docs];
    console.log(`number of docs  ${querySnapshot.length}`);
    let i = 0;

    for (const expiredDoc of querySnapshot) {


      i++;
      console.log(`Processing doc ${i} of ${querySnapshot.length}`);
      const doc = expiredDoc.data();
      var userName = doc.userName || null;
      var vehicleNum = doc.vehicleNum || null;
      var phone = doc.userContact ? doc.userContact.replace('+', '') : null;
      console.log(`Processing doc: ${doc.docName}`);
      if (vehicleNum === null) {

        const vehicleDoc = await db.collection('vehicles').doc(doc.vehicleId).get();
        if (vehicleDoc.exists) {
          vehicleNum = vehicleDoc.data().vehicleNumber;

        } else {

          console.warn(`Vehicle document with ID ${doc.vehicleId} does not exist.`);
          vehicleNum = "-"; // or handle accordingly
        }
      }
      // const documents = docData.documents || [];
      if (phone === null) {
        const userDoc = await db.collection('users').doc(doc.uId).get();
        if (userDoc.exists) {
          phone = userDoc.data().number;
          if (userName === null) {
            userName = userDoc.data().name = userDoc.data().name;
          }
        } else {

          console.warn(`User document with ID ${doc.uId} does not exist.`);
          phone = null; // or handle accordingly
          userName = "User";

        }
      }

      if (!phone) continue; // skip users with no phone
      console.log(`phone not null`);

      // for (const doc of documents) {
      const docName = doc.docName || 'Unnamed Document';
      const fieldData = doc.fieldData || [];

      for (const key in fieldData) {

        if (Object.hasOwnProperty.call(fieldData, key)) {
          const field = fieldData[key];
          const value = field.value;

          console.log(`Processing field: ${key}, value:`, value);

          // Check if this field is the expiry date (case-insensitive)
          if (key.toLowerCase() === 'expiry_date') {
            if (!value) {
              console.warn(`Warning: Expiry_Date field has no value.`);
              break;
            }

            // Convert Firestore Timestamp to JS Date if needed
            const expiryDate = value.toDate ? value.toDate() : value;

            if (!(expiryDate instanceof Date) || isNaN(expiryDate)) {
              console.warn(`Warning: Expiry_Date value is not a valid date:`, expiryDate);
              break;
            }

            const formattedDate = expiryDate.toDateString();
            console.log(`Expiry date found: ${formattedDate}`);

            // ==== SMS Message ====
            const smsText = `Hi ${userName}, the document "${docName}" is expiring on ${formattedDate}.`;
            console.log(`Sending SMS to ${phone}: ${smsText}`);

            // try {
            //   await sendSMS(phone, smsText);
            //   console.log(`SMS sent successfully to ${phone}`);
            // } catch (smsErr) {
            //   console.error("User doc SMS error:", smsErr);
            // }

            let fileData = null;

            // Check new structure: files array with at least one object having a non-empty value
            if (Array.isArray(doc.files) && doc.files.length > 0) {
              const firstObj = doc.files[0];
              const values = Object.values(firstObj);
              if (values.length > 0 && values[0] !== '') {
                fileData = values[0];
              }
            }

            // If new structure not present or empty or empty string, fallback to old structure
            if ((!fileData || fileData === '') && doc.fileUrl && doc.fileUrl !== '') {
              fileData = doc.fileUrl;
            }

            if (!fileData || fileData === '') {
              console.log('empty file');



              // ==== WhatsApp Message ====
              const wsData = {
                LicenseNumber: '84209111524',
                APIKey: 'Fy5kXjzh4IiSeaH7WGgw0rQlA',
                // contact: '917016229116',
                contact: phone,
                template: "expiry_reminder_noattach",
                param: `${userName},${docName},${vehicleNum || 'No'},${formattedDate}`,
                // fileUrl: publicUrl,
                // fileUrl: '',
                //  fileUrl:"https://datapartner.btpr.online/ProductPictures/84209111524_Insurance.pdf",
                // fileUrl: "https://storage.googleapis.com/massibs.firebasestorage.app/UserDocs%2F1749190028828.jpg",
                pdfName: doc.docName
              };

              console.log(`Sending WhatsApp message to ${phone} with data:`, wsData);

              try {
                await sendWSMessage(wsData);
                console.log(`WhatsApp message sent successfully to ${phone}`);
              } catch (wsErr) {
                console.error("User doc WhatsApp error:", wsErr);
              }
              try {
                await sendSMS(phone, '189812', userName, docName, vehicleNum, formattedDate);
              } catch (err) {
                console.error("onUserDocumentUpdate : SMS sending error:", err);
              }
              await new Promise(resolve => setTimeout(resolve, 1000)); // delays for 1 second
              break;
            }

            console.log(`data-${fileData}`);
            const url = fileData;
            console.log("DOC URL", url);
            // const match = (doc.fileUrl || '').match(/^(.*?\.pdf)/);

            // const trimmedUrl = match ? match[1] : (doc.fileUrl || '');
            var publicUrl = convertDownloadUrlToPublicUrl(url);
            // var publicUrl = convertDownloadUrlToPublicUrl('https://firebasestorage.googleapis.com/v0/b/massibs.firebasestorage.app/o/UserDocs%2F1748341198442.jpg?alt=media&token=3e9765ac-8215-46e2-b67b-fb5053379fb6');
            console.log(publicUrl);

            // ==== WhatsApp Message ====
            const wsData = {
              LicenseNumber: '84209111524',
              APIKey: 'Fy5kXjzh4IiSeaH7WGgw0rQlA',
              // contact: '917016229116',
              contact: phone,
              template: "expiry_reminder_v2",
              param: `${userName},${docName},${vehicleNum || 'No'},${formattedDate}`,
              fileUrl: publicUrl,
              // fileUrl: '',
              //  fileUrl:"https://datapartner.btpr.online/ProductPictures/84209111524_Insurance.pdf",
              // fileUrl: "https://storage.googleapis.com/massibs.firebasestorage.app/UserDocs%2F1749190028828.jpg",
              pdfName: doc.docName
            };

            console.log(`Sending WhatsApp message to ${phone} with data:`, wsData);

            try {
              await sendWSMessage(wsData);
              console.log(`WhatsApp message sent successfully to ${phone}`);
            } catch (wsErr) {
              console.error("User doc WhatsApp error:", wsErr);
            }
            try {
              await sendSMS(phone, '189812', userName, docName, vehicleNum, formattedDate);
            } catch (err) {
              console.error("onUserDocumentUpdate : SMS sending error:", err);
            }
            await new Promise(resolve => setTimeout(resolve, 1000)); // delays for 1 second
          } else {
            console.log(`Skipping field ${key}, not an expiry date.`);
          }
        }
      }

    }
  } catch (err) {
    console.error("Error processing user document expiries:", err);
  }

  return null;
});
exports.notifyUserDocumentAboutToExpiries = onSchedule({
  schedule: '0 0 * * *',
  timeZone: 'Asia/Kolkata',
  maxInstances: 1,
  timeoutSeconds: 540,
}, async (event) => {
  // exports.testers2 = onRequest(async (request, response) => {
  try {
    const checkDate = new Date();
    const midnightToday = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate(),
      0, 0, 0, 0
    );
    const plus30DaysMidnight = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() + 30,
      0, 0, 0, 0
    ); const plus30DaysMidnightEnd = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() + 31,
      0, 0, 0, 0
    );

    const plus10DaysMidnight = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() + 10,
      0, 0, 0, 0
    );
    const plus10DaysMidnightEnd = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() + 11,
      0, 0, 0, 0
    );

    const plus7DaysMidnight = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate() + 7,
      0, 0, 0, 0
    )

    const midnightTimestamp = Timestamp.fromDate(midnightToday);
    const plus30DaysTimestamp = Timestamp.fromDate(plus30DaysMidnight);
    const plus30DaysTimestampEnd = Timestamp.fromDate(plus30DaysMidnightEnd);

    const plus10DaysTimestamp = Timestamp.fromDate(plus10DaysMidnight);
    const plus10DaysTimestampEnd = Timestamp.fromDate(plus10DaysMidnightEnd);

    const plus7DaysTimestamp = Timestamp.fromDate(plus7DaysMidnight);

    const querySnapshot1 = await db.collection('usersDocs')
      .where('vehicleDoc', '==', true)
      .where('isArchived', '==', false)
      .where('docName', 'in', ['Vehicle Fitness', 'Insurance', 'Vehicle Permit'])

      .where('fieldData.Expiry_Date.value', '>=', plus30DaysTimestamp)
      .where('fieldData.Expiry_Date.value', '<', plus30DaysTimestampEnd)
      .get();

    const querySnapshot2 = await db.collection('usersDocs')
      .where('vehicleDoc', '==', true)
      .where('isArchived', '==', false)
      .where('docName', 'in', ['Vehicle Fitness', 'Insurance', 'Vehicle Permit'])

      .where('fieldData.Expiry_Date.value', '>=', plus10DaysTimestamp)
      .where('fieldData.Expiry_Date.value', '<', plus10DaysTimestampEnd)
      .get();
    console.log(`number of docs  ${plus10DaysTimestamp.toDate()}`);
    console.log(`number of docs  ${querySnapshot2.docs.length}`);
    const querySnapshot3 = await db.collection('usersDocs')
      .where('vehicleDoc', '==', true)
      .where('isArchived', '==', false)
      .where('docName', 'in', ['Vehicle Fitness', 'Insurance', 'Vehicle Permit'])
      .where('fieldData.Expiry_Date.value', '>=', midnightTimestamp)
      .where('fieldData.Expiry_Date.value', '<', plus7DaysTimestamp)
      .get();
    // console.log(`number of docs  ${querySnapshot.docs.length}`);

    let i = 0;
    const querySnapshot = [...querySnapshot2.docs,
    ...querySnapshot2.docs,
    ...querySnapshot3.docs];
    for (const expiredDoc of querySnapshot) {

      console.log(`Processing doc ${i} of ${querySnapshot.length}`);
      const doc = expiredDoc.data();
      var userName = doc.userName || null;
      var vehicleNum = doc.vehicleNum || null;
      var phone = doc.userContact ? doc.userContact.replace('+', '') : null;
      console.log(`Processing doc: ${doc.docName}`);
      if (vehicleNum === null) {

        const vehicleDoc = await db.collection('vehicles').doc(doc.vehicleId).get();
        if (vehicleDoc.exists) {
          vehicleNum = vehicleDoc.data().vehicleNumber;

        } else {

          console.warn(`Vehicle document with ID ${doc.vehicleId} does not exist.`);
          vehicleNum = "-"; // or handle accordingly
        }
      }
      if (phone === null) {
        const userDoc = await db.collection('users').doc(doc.uId).get();
        if (userDoc.exists) {
          phone = userDoc.data().number;
          if (userName === null) {
            userName = userDoc.data().name = userDoc.data().name;
          }
        } else {
          console.warn(`User document with ID ${doc.uId} does not exist.`);
          phone = null;
          userName = "User";
          // or handle accordingly
        }
      }
      // const documents = docData.documents || [];

      console.log(`check phone ${phone}`);
      if (!phone) continue; // skip users with no phone
      console.log(`phone not null`);

      // for (const doc of documents) {
      const docName = doc.docName || 'Unnamed Document';
      const fieldData = doc.fieldData || [];

      for (const key in fieldData) {
        if (Object.hasOwnProperty.call(fieldData, key)) {
          const field = fieldData[key];
          const value = field.value;

          console.log(`Processing field: ${key}, value:`, value);

          // Check if this field is the expiry date (case-insensitive)
          if (key.toLowerCase() === 'expiry_date') {
            if (!value) {
              console.warn(`Warning: Expiry_Date field has no value.`);
              break;
            }

            // Convert Firestore Timestamp to JS Date if needed
            const expiryDate = value.toDate ? value.toDate() : value;

            if (!(expiryDate instanceof Date) || isNaN(expiryDate)) {
              console.warn(`Warning: Expiry_Date value is not a valid date:`, expiryDate);
              break;
            }

            const formattedDate = expiryDate.toDateString();
            console.log(`Expiry date found: ${formattedDate}`);

            // ==== SMS Message ====

            const smsText = `Hi ${userName}, the document "${docName}" is expiring on ${formattedDate}.`;
            console.log(`Sending SMS to ${phone}: ${smsText}`);

            // try {
            //   await sendSMS(phone, smsText);
            //   console.log(`SMS sent successfully to ${phone}`);
            // } catch (smsErr) {
            //   console.error("User doc SMS error:", smsErr);
            // }


            let fileData = null;

            // Check new structure: files array with at least one object having a non-empty value
            if (Array.isArray(doc.files) && doc.files.length > 0) {
              const firstObj = doc.files[0];
              const values = Object.values(firstObj);
              if (values.length > 0 && values[0] !== '') {
                fileData = values[0];
              }
            }

            // If new structure not present or empty or empty string, fallback to old structure
            if ((!fileData || fileData === '') && doc.fileUrl && doc.fileUrl !== '') {
              fileData = doc.fileUrl;
            }

            if (!fileData || fileData === '') {
              console.log('empty file');
              console.log(`data-${fileData}`);
              // const url = fileData;
              // console.log("DOC URL", url);

              // var publicUrl = convertDownloadUrlToPublicUrl(url);
              // var publicUrl = convertDownloadUrlToPublicUrl('https://firebasestorage.googleapis.com/v0/b/massibs.firebasestorage.app/o/UserDocs%2F1748341198442.jpg?alt=media&token=3e9765ac-8215-46e2-b67b-fb5053379fb6');
              // console.log(publicUrl);

              // ==== WhatsApp Message ====
              const wsData = {
                LicenseNumber: '84209111524',
                APIKey: 'Fy5kXjzh4IiSeaH7WGgw0rQlA',
                contact: phone,
                // contact: '917016229116',
                template: "vehicle_expiry_reminder_noattch",
                param: `${userName},${docName},${vehicleNum || 'No'},${formattedDate}`,
                // fileUrl: publicUrl,
                // fileUrl: '',
                //  fileUrl:"https://datapartner.btpr.online/ProductPictures/84209111524_Insurance.pdf",
                // fileUrl: "https://storage.googleapis.com/massibs.firebasestorage.app/UserDocs%2F1749190028828.jpg",
                pdfName: doc.docName
              };

              console.log(`Sending WhatsApp message to ${phone} with data:`, wsData);

              try {
                sendWSMessage(wsData);
                console.log(`WhatsApp message sent successfully to ${phone}`);

              } catch (wsErr) {
                console.error("User doc WhatsApp error:", wsErr);


              }
              try {
                await sendSMS(phone, '189813', userName, docName, vehicleNum, formattedDate);
              } catch (err) {
                console.error("onUserDocumentUpdate : SMS sending error:", err);
              }
              await new Promise(resolve => setTimeout(resolve, 1000)); // delays for 1 second
              break;
            }

            console.log(`data-${fileData}`);
            const url = fileData;
            console.log("DOC URL", url);
            // const match = (doc.fileUrl || '').match(/^(.*?\.pdf)/);

            // const trimmedUrl = match ? match[1] : (doc.fileUrl || '');
            var publicUrl = convertDownloadUrlToPublicUrl(url);
            // var publicUrl = convertDownloadUrlToPublicUrl('https://firebasestorage.googleapis.com/v0/b/massibs.firebasestorage.app/o/UserDocs%2F1748341198442.jpg?alt=media&token=3e9765ac-8215-46e2-b67b-fb5053379fb6');
            console.log(publicUrl);

            // ==== WhatsApp Message ====
            const wsData = {
              LicenseNumber: '84209111524',
              APIKey: 'Fy5kXjzh4IiSeaH7WGgw0rQlA',
              contact: phone,
              // contact: '917016229116',
              template: "vehicle_expiry_reminder",
              param: `${userName},${docName},${vehicleNum || 'No'},${formattedDate}`,
              fileUrl: publicUrl,
              // fileUrl: '',
              //  fileUrl:"https://datapartner.btpr.online/ProductPictures/84209111524_Insurance.pdf",
              // fileUrl: "https://storage.googleapis.com/massibs.firebasestorage.app/UserDocs%2F1749190028828.jpg",
              pdfName: doc.docName
            };

            console.log(`Sending WhatsApp message to ${phone} with data:`, wsData);

            try {
              sendWSMessage(wsData);
              console.log(`WhatsApp message sent successfully to ${phone}`);

            } catch (wsErr) {
              console.error("User doc WhatsApp error:", wsErr);


            }
            try {
              await sendSMS(phone, '189813', userName, docName, vehicleNum, formattedDate);
            } catch (err) {
              console.error("onUserDocumentUpdate : SMS sending error:", err);
            }
            await new Promise(resolve => setTimeout(resolve, 1000)); // delays for 1 second
          } else {
            console.log(`Skipping field ${key}, not an expiry date.`);

          }
        }
      }
      console.log(`doc processed ${i}`);

    }
  } catch (err) {
    console.error("Error processing user document expiries:", err);
  }

  return null;
});

