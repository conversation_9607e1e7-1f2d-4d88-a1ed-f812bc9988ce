name: mass_ibs
description: "A new Flutter project."
version: 1.0.0+18

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter

  # Core
  get: ^4.7.2
  shared_preferences: ^2.5.3

  # Firebase
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.2
  firebase_messaging: ^15.2.5
  firebase_analytics: ^11.4.5
  flutter_local_notifications: ^19.1.0
  cloud_functions: ^5.4.0
  cloud_firestore: ^5.4.1
  firebase_storage: ^12.3.0
  firebase_ui_auth: ^1.15.0
  firebase_app_check: ^0.3.2+5

  # UI
  fluttertoast: ^8.2.12
  flutter_svg: ^2.0.17
  extended_image: ^10.0.1
  cupertino_icons: ^1.0.6
  google_fonts: ^6.2.1
  intl: ^0.19.0
  go_router: ^15.0.0
  flutter_staggered_grid_view: ^0.7.0
  carousel_slider: ^5.0.0
  cached_network_image: ^3.4.1
  file_picker: 9.2.3
  flutter_image_compress: ^2.0.4
  image_picker: ^1.0.4
  flutter_typeahead: ^5.2.0
  loading_animation_widget: ^1.3.0
  uuid: 4.5.1
  url_launcher: ^6.3.1
  flutter_pdfview: ^1.4.0+1
  path_provider: ^2.1.5
  share_plus: ^11.0.0
  webview_flutter: ^4.11.0
  syncfusion_flutter_pdfviewer: ^29.2.4
  flutter_html: ^3.0.0
  qr_flutter: ^4.1.0
  date_format_field: ^0.1.0 
  html_editor_enhanced:
    git:
      url: https://github.com/MetaDC/html-editor-enhanced.git
      ref: master
  in_app_update: ^4.2.3
  http: any
  

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/svgs/

  fonts:
    - family: Lexend
      fonts:
        - asset: assets/fonts/Lexend_Variable.ttf
